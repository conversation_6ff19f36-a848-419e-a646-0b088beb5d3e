package com.example.floatingai;

import android.os.Bundle;
import android.content.SharedPreferences;
import android.widget.EditText;
import android.widget.Button;
import android.widget.Spinner;
import android.widget.ArrayAdapter;
import android.widget.Toast;
import android.widget.TextView;
import android.widget.LinearLayout;
import android.view.View;
import android.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import java.util.List;
import android.util.Log;
import android.content.Intent;

public class SettingsActivity extends AppCompatActivity {
    private EditText apiKeyInput;
    private EditText customModelInput;
    private EditText customModelNameInput;
    private Button addCustomModelButton;
    private Button clearFavoritesButton;
    private LinearLayout favoriteModelsContainer;
    private TextView selectedModelDisplay;
    
    private SharedPreferences sharedPreferences;
    private ModelManager modelManager;
    private ModelManager.AIModel currentSelectedModel;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_settings);

        // Inicializar componentes
        initializeViews();
        
        sharedPreferences = getSharedPreferences("app_prefs", MODE_PRIVATE);
        modelManager = new ModelManager(this);

        // Configurar listeners
        setupListeners();
        
        // Cargar datos existentes
        loadExistingData();
        
        // Actualizar UI
        updateFavoriteModelsUI();
        updateSelectedModelDisplay();

        Button saveButton = findViewById(R.id.save_settings_button);
    }

    private void initializeViews() {
        apiKeyInput = findViewById(R.id.api_key_input);
        customModelInput = findViewById(R.id.custom_model_input);
        customModelNameInput = findViewById(R.id.custom_model_name_input);
        addCustomModelButton = findViewById(R.id.add_custom_model_button);
        clearFavoritesButton = findViewById(R.id.clear_favorites_button);
        favoriteModelsContainer = findViewById(R.id.favorite_models_container);
        selectedModelDisplay = findViewById(R.id.selected_model_display);
    }

    private void setupListeners() {
        // Botón para agregar modelo personalizado
        addCustomModelButton.setOnClickListener(v -> addCustomModel());
        
        // Botón para limpiar favoritos
        clearFavoritesButton.setOnClickListener(v -> clearFavorites());
        
        // Botón guardar
        Button saveButton = findViewById(R.id.save_settings_button);
        saveButton.setOnClickListener(v -> saveConfiguration());
    }

    private void loadExistingData() {
        // Cargar API key
        apiKeyInput.setText(sharedPreferences.getString("api_key", ""));
        
        // Cargar modelo seleccionado
        String savedModelId = sharedPreferences.getString("selected_model", null);
        
        if (savedModelId != null) {
            ModelManager.AIModel savedModel = modelManager.findModelById(savedModelId);
            if (savedModel != null) {
                currentSelectedModel = savedModel;
            } else {
                // Model ID was saved, but model not found (e.g. deleted), so no model is selected.
                currentSelectedModel = null; 
            }
        } else {
            // No model ID saved, so no model is selected.
            currentSelectedModel = null;
        }
    }

    private void addCustomModel() {
        String modelId = customModelInput.getText().toString().trim();
        String displayName = customModelNameInput.getText().toString().trim();
        
        if (modelId.isEmpty()) {
            Toast.makeText(this, "Por favor ingresa el ID del modelo", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (!modelManager.isValidModelId(modelId)) {
            Toast.makeText(this, "ID de modelo inválido. Debe tener formato: proveedor/modelo", 
                    Toast.LENGTH_LONG).show();
            return;
        }
        
        // Generar nombre si no se proporcionó
        if (displayName.isEmpty()) {
            displayName = modelManager.generateDisplayName(modelId);
        }
        
        // Crear y agregar modelo
        ModelManager.AIModel newModel = new ModelManager.AIModel(modelId, displayName);
        modelManager.addFavoriteModel(newModel);
        
        // Limpiar inputs
        customModelInput.setText("");
        customModelNameInput.setText("");
        
        // Actualizar UI
        updateFavoriteModelsUI();
        
        Toast.makeText(this, "Modelo agregado a favoritos: " + displayName, Toast.LENGTH_SHORT).show();
    }

    private void updateFavoriteModelsUI() {
        favoriteModelsContainer.removeAllViews();
        
        List<ModelManager.AIModel> favoriteModels = modelManager.getFavoriteModels();
        
        if (favoriteModels.isEmpty()) {
            TextView emptyView = new TextView(this);
            emptyView.setText("No hay modelos favoritos");
            emptyView.setTextColor(getResources().getColor(android.R.color.darker_gray));
            emptyView.setPadding(12, 12, 12, 12);
            favoriteModelsContainer.addView(emptyView);
            return;
        }
        
        for (ModelManager.AIModel model : favoriteModels) {
            Button modelButton = new Button(this);
            modelButton.setText(model.displayName);
            modelButton.setLayoutParams(new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT));
            
            // Estilo del botón
            modelButton.setBackgroundColor(getResources().getColor(android.R.color.holo_blue_light));
            modelButton.setTextColor(getResources().getColor(android.R.color.white));
            modelButton.setPadding(16, 12, 16, 12);
            
            // Margen
            LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) modelButton.getLayoutParams();
            params.setMargins(0, 0, 0, 8);
            modelButton.setLayoutParams(params);
            
            // Click listener para seleccionar
            modelButton.setOnClickListener(v -> {
                currentSelectedModel = model;
                updateSelectedModelDisplay();
                Toast.makeText(this, "Modelo seleccionado: " + model.displayName, Toast.LENGTH_SHORT).show();
            });
            
            // Long click listener para eliminar
            modelButton.setOnLongClickListener(v -> {
                showDeleteModelDialog(model);
                return true;
            });
            
            favoriteModelsContainer.addView(modelButton);
        }
    }

    private void showDeleteModelDialog(ModelManager.AIModel model) {
        new AlertDialog.Builder(this)
                .setTitle("Eliminar Modelo")
                .setMessage("¿Estás seguro de que quieres eliminar '" + model.displayName + "' de tus favoritos?")
                .setPositiveButton("Eliminar", (dialog, which) -> {
                    modelManager.removeFavoriteModel(model.id);
                    updateFavoriteModelsUI();
                    
                    // Si era el modelo seleccionado, deseleccionarlo
                    if (currentSelectedModel != null && currentSelectedModel.id.equals(model.id)) {
                        currentSelectedModel = null; // Deselect if it was the one deleted
                        updateSelectedModelDisplay(); 
                    }
                    
                    Toast.makeText(this, "Modelo eliminado", Toast.LENGTH_SHORT).show();
                })
                .setNegativeButton("Cancelar", null)
                .show();
    }

    private void clearFavorites() {
        List<ModelManager.AIModel> favorites = modelManager.getFavoriteModels();
        if (favorites.isEmpty()) {
            Toast.makeText(this, "No hay modelos favoritos para eliminar", Toast.LENGTH_SHORT).show();
            return;
        }
        
        new AlertDialog.Builder(this)
                .setTitle("Limpiar Favoritos")
                .setMessage("¿Estás seguro de que quieres eliminar todos los modelos favoritos?")
                .setPositiveButton("Limpiar", (dialog, which) -> {
                    modelManager.clearFavoriteModels();
                    updateFavoriteModelsUI();
                    
                    // If the current selected model was a favorite, it's now gone.
                    // Set currentSelectedModel to null.
                    if (currentSelectedModel != null) {
                        // Check if the currentSelectedModel was in the list of favorites that was just cleared.
                        // Since all models are now 'favorites', if currentSelectedModel existed, it was cleared.
                        currentSelectedModel = null;
                        updateSelectedModelDisplay();
                    }
                    
                    Toast.makeText(this, "Todos los favoritos eliminados", Toast.LENGTH_SHORT).show();
                })
                .setNegativeButton("Cancelar", null)
                .show();
    }

    private void updateSelectedModelDisplay() {
        if (currentSelectedModel != null) {
            String displayText = currentSelectedModel.displayName + "\n" +
                               "ID: " + currentSelectedModel.id; // Removed "Tipo"
            selectedModelDisplay.setText(displayText);
            selectedModelDisplay.setVisibility(View.VISIBLE);
        } else {
            selectedModelDisplay.setText("Ningún modelo seleccionado. Por favor, añade y selecciona un modelo.");
            // selectedModelDisplay.setVisibility(View.GONE); // Or show a message
        }
    }

    private void saveConfiguration() {
        String apiKey = apiKeyInput.getText().toString().trim();
        
        Log.d("SettingsActivity", "Saving configuration - API key length: " + apiKey.length());
        
        if (apiKey.isEmpty()) {
            Toast.makeText(this, "Por favor ingresa tu API key de OpenRouter", Toast.LENGTH_LONG).show();
            return;
        }

        // It's okay if no model is selected, the service will handle it.
        // if (currentSelectedModel == null) {
        //     Toast.makeText(this, "Por favor selecciona un modelo", Toast.LENGTH_SHORT).show();
        //     return;
        // }
        
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putString("api_key", apiKey);
        
        if (currentSelectedModel != null) {
            editor.putString("selected_model", currentSelectedModel.id);
        } else {
            editor.remove("selected_model"); // Remove if no model is selected
        }
        
        editor.apply(); // Using apply for asynchronous saving
        
        Toast.makeText(this, "Configuración guardada", Toast.LENGTH_SHORT).show();
        Log.d("SettingsActivity", "Configuration saved. API Key: " + apiKeyInput.getText().toString() + ", Model: " + (currentSelectedModel != null ? currentSelectedModel.id : "None"));
        
        // Opcional: finalizar la actividad o dar feedback al usuario
        // finish();
    }
}