<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:fitsSystemWindows="true"
    tools:context=".ConversationViewActivity">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/messages_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:scrollbars="vertical" />

    <TextView
        android:id="@+id/empty_messages_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="No hay mensajes en esta conversación."
        android:gravity="center"
        android:textSize="16sp"
        android:visibility="gone"
        android:layout_marginTop="16dp"/>

    <!-- Optional: Button to delete this specific conversation -->
    <Button
        android:id="@+id/delete_conversation_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Eliminar Conversación"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="16dp"
        android:backgroundTint="@android:color/holo_red_light" />

</LinearLayout> 