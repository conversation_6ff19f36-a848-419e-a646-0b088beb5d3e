<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:fitsSystemWindows="true">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar_other_options"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        app:titleTextColor="@android:color/black"
        app:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Temperatura (0.0 - 1.0)"
                android:textSize="16sp"
                android:layout_marginTop="8dp"/>
            <EditText
                android:id="@+id/temperature_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="numberDecimal"
                android:hint="Ej: 0.7"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ventana de Contexto (mensajes)"
                android:textSize="16sp"
                android:layout_marginTop="16dp"/>
            <EditText
                android:id="@+id/context_window_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="number"
                android:hint="Ej: 10"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Max Tokens (respuesta IA)"
                android:textSize="16sp"
                android:layout_marginTop="16dp"/>
            <EditText
                android:id="@+id/max_tokens_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="number"
                android:hint="Ej: 1000"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="System Prompt (Instrucción inicial para la IA)"
                android:textSize="16sp"
                android:layout_marginTop="16dp"/>
            <EditText
                android:id="@+id/system_prompt_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textMultiLine"
                android:minLines="3"
                android:gravity="top|start"
                android:hint="Ej: Eres un asistente útil y amigable."/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Tamaño de Fuente del Chat"
                android:textSize="16sp"
                android:layout_marginTop="16dp"/>
            <RadioGroup
                android:id="@+id/font_size_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <RadioButton
                    android:id="@+id/font_small"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Pequeño"/>
                <RadioButton
                    android:id="@+id/font_medium"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Mediano"
                    android:checked="true"/>
                <RadioButton
                    android:id="@+id/font_large"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Grande"/>
            </RadioGroup>

            <CheckBox
                android:id="@+id/send_with_enter_checkbox"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Enviar mensaje con tecla ENTER"
                android:textSize="16sp"
                android:layout_marginTop="24dp"/>

            <CheckBox
                android:id="@+id/close_on_outside_checkbox"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Cerrar chat flotante al tocar fuera de la ventana"
                android:textSize="16sp"
                android:layout_marginTop="16dp"/>

            <CheckBox
                android:id="@+id/save_captures_in_gallery_checkbox"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Guardar capturas de pantalla en la galería"
                android:textSize="16sp"
                android:layout_marginTop="16dp"/>

            <!-- Configurable Prompt Button 1 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Texto del Botón de Prompt 1"
                android:textSize="16sp"
                android:layout_marginTop="24dp"/>
            <EditText
                android:id="@+id/prompt_button_1_text_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:hint="Ej: Explica la imagen"/>
            <CheckBox
                android:id="@+id/prompt_button_1_enable_checkbox"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Habilitar Botón de Prompt 1"
                android:textSize="16sp"
                android:layout_marginTop="8dp"/>

            <!-- Configurable Prompt Button 2 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Texto del Botón de Prompt 2"
                android:textSize="16sp"
                android:layout_marginTop="16dp"/>
            <EditText
                android:id="@+id/prompt_button_2_text_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:hint="Ej: Resume el texto"/>
            <CheckBox
                android:id="@+id/prompt_button_2_enable_checkbox"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Habilitar Botón de Prompt 2"
                android:textSize="16sp"
                android:layout_marginTop="8dp"/>

            <!-- Configurable Prompt Button 3 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Texto del Botón de Prompt 3"
                android:textSize="16sp"
                android:layout_marginTop="16dp"/>
            <EditText
                android:id="@+id/prompt_button_3_text_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:hint="Ej: Analiza la imagen"/>
            <CheckBox
                android:id="@+id/prompt_button_3_enable_checkbox"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Habilitar Botón de Prompt 3"
                android:textSize="16sp"
                android:layout_marginTop="8dp"/>

            <!-- Configurable Prompt Button 4 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Texto del Botón de Prompt 4"
                android:textSize="16sp"
                android:layout_marginTop="16dp"/>
            <EditText
                android:id="@+id/prompt_button_4_text_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:hint="Ej: Traduce el texto"/>
            <CheckBox
                android:id="@+id/prompt_button_4_enable_checkbox"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Habilitar Botón de Prompt 4"
                android:textSize="16sp"
                android:layout_marginTop="8dp"/>

        </LinearLayout>
    </ScrollView>

    <Button
        android:id="@+id/save_other_options_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Guardar Configuración"
        android:layout_gravity="center_horizontal"
        android:layout_margin="16dp"
        android:backgroundTint="?attr/colorPrimary"
        android:textColor="@android:color/white"/>

</LinearLayout> 