package com.example.floatingai;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.provider.BaseColumns;

public class ChatHistoryDbHelper extends SQLiteOpenHelper {

    public static final int DATABASE_VERSION = 2; // Incremented version
    public static final String DATABASE_NAME = "ChatHistory.db";

    // Inner class for Conversations table
    public static class ConversationEntry implements BaseColumns {
        public static final String TABLE_NAME = "conversations";
        public static final String COLUMN_NAME_START_TIMESTAMP = "start_timestamp";
        // Potentially add a title/summary column later
        // public static final String COLUMN_NAME_TITLE = "title";
    }

    public static class MessageEntry implements BaseColumns {
        public static final String TABLE_NAME = "messages";
        public static final String COLUMN_NAME_SENDER = "sender";
        public static final String COLUMN_NAME_MESSAGE_CONTENT = "message_content";
        public static final String COLUMN_NAME_TIMESTAMP = "timestamp";
        public static final String COLUMN_NAME_CONVERSATION_ID = "conversation_id"; // Foreign key
    }

    private static final String SQL_CREATE_CONVERSATION_TABLE =
            "CREATE TABLE " + ConversationEntry.TABLE_NAME + " (" +
            ConversationEntry._ID + " INTEGER PRIMARY KEY AUTOINCREMENT," +
            ConversationEntry.COLUMN_NAME_START_TIMESTAMP + " INTEGER DEFAULT (STRFTIME('%s', 'now') * 1000))";

    private static final String SQL_CREATE_MESSAGE_TABLE =
            "CREATE TABLE " + MessageEntry.TABLE_NAME + " (" +
            MessageEntry._ID + " INTEGER PRIMARY KEY AUTOINCREMENT," +
            MessageEntry.COLUMN_NAME_SENDER + " TEXT," +
            MessageEntry.COLUMN_NAME_MESSAGE_CONTENT + " TEXT," +
            MessageEntry.COLUMN_NAME_TIMESTAMP + " INTEGER DEFAULT (STRFTIME('%s', 'now') * 1000)," + // Store as milliseconds
            MessageEntry.COLUMN_NAME_CONVERSATION_ID + " INTEGER," +
            "FOREIGN KEY(" + MessageEntry.COLUMN_NAME_CONVERSATION_ID + ") REFERENCES " +
            ConversationEntry.TABLE_NAME + "(" + ConversationEntry._ID + ") ON DELETE CASCADE)";


    private static final String SQL_DELETE_MESSAGE_TABLE =
            "DROP TABLE IF EXISTS " + MessageEntry.TABLE_NAME;
    private static final String SQL_DELETE_CONVERSATION_TABLE =
            "DROP TABLE IF EXISTS " + ConversationEntry.TABLE_NAME;

    public ChatHistoryDbHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        db.execSQL(SQL_CREATE_CONVERSATION_TABLE);
        db.execSQL(SQL_CREATE_MESSAGE_TABLE);
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        // This database is only a cache for online data, so its upgrade policy is
        // to simply to discard the data and start over
        db.execSQL(SQL_DELETE_MESSAGE_TABLE);
        db.execSQL(SQL_DELETE_CONVERSATION_TABLE);
        onCreate(db);
    }

    @Override
    public void onDowngrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        onUpgrade(db, oldVersion, newVersion);
    }
} 