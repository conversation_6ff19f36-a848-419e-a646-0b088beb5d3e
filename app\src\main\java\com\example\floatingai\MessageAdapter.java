package com.example.floatingai;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.List;

public class MessageAdapter extends RecyclerView.Adapter<MessageAdapter.MessageViewHolder> {

    private Context context;
    private List<ChatMessage> messageList;

    public MessageAdapter(Context context, List<ChatMessage> initialMessageList) {
        this.context = context;
        this.messageList = new ArrayList<>(initialMessageList);
    }

    @NonNull
    @Override
    public MessageViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_message, parent, false);
        return new MessageViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull MessageViewHolder holder, int position) {
        ChatMessage message = messageList.get(position);
        holder.senderText.setText(message.getSender() + ":");
        holder.contentText.setText(message.getContent());
    }

    @Override
    public int getItemCount() {
        return messageList.size();
    }

    public void updateData(List<ChatMessage> newMessageList) {
        this.messageList.clear();
        if (newMessageList != null) {
            this.messageList.addAll(newMessageList);
        }
        notifyDataSetChanged();
    }

    static class MessageViewHolder extends RecyclerView.ViewHolder {
        TextView senderText;
        TextView contentText;

        MessageViewHolder(View itemView) {
            super(itemView);
            senderText = itemView.findViewById(R.id.message_sender_text);
            contentText = itemView.findViewById(R.id.message_content_text);
        }
    }

    // Helper class for chat messages (can be expanded later)
    public static class ChatMessage {
        private String sender;
        private String content;
        // private long timestamp; // If needed for display

        public ChatMessage(String sender, String content) {
            this.sender = sender;
            this.content = content;
        }

        public String getSender() {
            return sender;
        }

        public String getContent() {
            return content;
        }
    }
} 