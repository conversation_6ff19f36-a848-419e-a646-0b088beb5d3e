package com.example.floatingai;

import android.app.Activity;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.content.pm.ServiceInfo;
import android.content.ContentResolver;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.PixelFormat;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.provider.Settings;
import android.util.Base64;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.ImageButton;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.text.TextWatcher;
import android.text.Editable;
import android.view.ViewGroup;
import android.text.Html;

import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.concurrent.TimeUnit;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.ActionMode;
import android.widget.PopupMenu;

import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.content.ContentValues;
import java.util.List;
import java.text.SimpleDateFormat;

public class FloatingService extends Service {

    private static final String TAG = "FloatingService";
    private static final String CHANNEL_ID = "FloatingServiceChannel";
    
    private WindowManager windowManager;
    private View floatingBubble;
    private View chatWindow;
    private ImageView dismissView; // For the X icon
    private WindowManager.LayoutParams dismissParams;
    private boolean isDismissViewVisible = false;
    private boolean isBubbleOverDismissArea = false;
    
    // Para la pulsación larga
    private Handler longPressHandler = new Handler(Looper.getMainLooper());
    private Runnable longPressRunnable;
    private static final int LONG_PRESS_TIMEOUT = 300; // milisegundos
    private boolean isLongPressDetected = false;
    private Runnable titleLongPressRunnable; // Added for title long press
    
    private boolean isChatVisible = false;
    
    private OpenRouterAPI apiService;
    private String apiKey;
    private String selectedModel;
    private float aiTemperature;
    private int aiContextWindow;
    private int aiMaxTokens;
    private String chatFontSize;
    private boolean sendWithEnterEnabled;
    private String aiSystemPrompt; // Added for system prompt

    // Variables for configurable prompt buttons
    private String promptButton1Text;
    private boolean promptButton1Enabled;
    private String promptButton2Text;
    private boolean promptButton2Enabled;
    // Adding variables for buttons 3 and 4
    private String promptButton3Text;
    private boolean promptButton3Enabled;
    private String promptButton4Text;
    private boolean promptButton4Enabled;
    private LinearLayout promptButtonsContainer; // Placeholder for buttons in chat_window.xml
    private Bitmap currentActiveImageForPromptButtons; // To hold the image for prompt buttons

    private LinearLayout chatHistory;
    private EditText messageInput;
    private ScrollView chatScrollView;
    private ImageButton selectModelButton; // Added for model selection
    
    // Variables para hacer la burbuja arrastrable
    private WindowManager.LayoutParams bubbleParams;
    private int initialX, initialY;
    private float initialTouchX, initialTouchY;
    
    // Variable para imagen compartida
    private Bitmap currentSharedImage;
    private Bitmap pendingCapturedImageBitmap;
    
    // Variables para hacer la ventana de chat arrastrable
    private WindowManager.LayoutParams chatParams;
    private int chatInitialX, chatInitialY;
    private float chatInitialTouchX, chatInitialTouchY;
    private float dragCheckRawX, dragCheckRawY; // Renamed for clarity

    // Variables para redimensionar la ventana de chat
    private boolean isResizing = false;
    private static final int RESIZE_MODE_NONE = 0;
    private static final int RESIZE_MODE_BOTTOM_LEFT = 1;
    private static final int RESIZE_MODE_BOTTOM = 2;
    private static final int RESIZE_MODE_BOTTOM_RIGHT = 3;
    private int currentResizeMode = RESIZE_MODE_NONE;
    private int resizeStartWidth, resizeStartHeight, resizeStartX;
    private float initialTouchX_resize, initialTouchY_resize;
    private int minChatWidthPx, minChatHeightPx, maxChatWidthPx, maxChatHeightPx;
    private int resizeHandleAreaPx;

    // Variables for new_chat_button interaction
    private boolean isPotentialTapOnNewChatButton = false;
    private boolean isDraggingChatWindowViaNewChatButton = false;
    private Handler newChatButtonHandler = new Handler(Looper.getMainLooper());
    private Runnable startDragRunnable;
    private float newChatButtonDownRawX, newChatButtonDownRawY;
    private static final int START_DRAG_DELAY = 200; // ms for differentiating tap from drag initiation
    private static final float CLICK_DRAG_TOLERANCE_BUTTON = 15f; // dp, will convert to px

    // Variables for select_model_button interaction
    private boolean isPotentialTapOnSelectModelButton = false;
    private boolean isDraggingChatWindowViaSelectModelButton = false;
    private Handler selectModelButtonHandler = new Handler(Looper.getMainLooper());
    private Runnable startDragSelectModelRunnable;
    private float selectModelButtonDownRawX, selectModelButtonDownRawY;

    // Variables for close_button interaction
    private boolean isPotentialTapOnCloseButton = false;
    private boolean isDraggingChatWindowViaCloseButton = false;
    private Handler closeButtonHandler = new Handler(Looper.getMainLooper());
    private Runnable startDragCloseButtonRunnable;
    private float closeButtonDownRawX, closeButtonDownRawY;

    private boolean wasChatVisibleBeforeCapture = false;
    private boolean isChatUiInitialized = false;
    public static final String ACTION_RESTORE_CHAT_WINDOW = "com.example.floatingai.RESTORE_CHAT_WINDOW";
    public static final String ACTION_IMAGE_CAPTURED_FOR_CHAT = "com.example.floatingai.IMAGE_CAPTURED_FOR_CHAT";

    private Long currentConversationId = null;

    // Variables for dynamic message input height
    private int singleLineInputHeight = -1;
    private static final int MAX_INPUT_LINES = 5;

    // Variables for title bar dragging
    private boolean isPotentialTapOnTitle = false;
    private boolean isDraggingChatWindowViaTitle = false;
    private Handler titleHandler = new Handler(Looper.getMainLooper());
    private Runnable startDragTitleRunnable;
    private float titleDownRawX, titleDownRawY;

    private BroadcastReceiver captureCancelReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            Log.d(TAG, "captureCancelReceiver: Received broadcast. wasChatVisibleBeforeCapture = " + wasChatVisibleBeforeCapture);
            if (ACTION_RESTORE_CHAT_WINDOW.equals(intent.getAction())) {
                Log.d(TAG, "Received broadcast to restore chat window.");
                if (wasChatVisibleBeforeCapture) {
                    Log.d(TAG, "captureCancelReceiver: Condition met, calling showChatWindow()");
                    showChatWindow();
                }
                wasChatVisibleBeforeCapture = false; // Reset flag
            }
        }
    };

    private ChatHistoryDbHelper dbHelper;

    private boolean closeOnOutsideEnabled = false; // Nueva variable para la opción
    
    // Variables for saving window state with delay
    private Handler saveStateHandler = new Handler(Looper.getMainLooper());
    private Runnable saveStateRunnable;
    private static final int SAVE_STATE_DELAY = 1000; // 1 second delay to avoid constant saving during drag/resize
    
    // Variable para controlar si se guardan las capturas en la galería
    private boolean saveCapturesInGallery = false;
    
    // Receptor para cambios de orientación
    private final BroadcastReceiver orientationChangeReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction().equals(Intent.ACTION_CONFIGURATION_CHANGED)) {
                adjustChatWindowToOrientation();
            }
        }
    };

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        
        Log.d(TAG, "FloatingService onCreate() called");
        
        dbHelper = new ChatHistoryDbHelper(this);
        
        // Initialize resize parameters
        minChatWidthPx = dpToPx(200);
        minChatHeightPx = dpToPx(150);
        resizeHandleAreaPx = dpToPx(40); // 40dp area for resize handles

        android.util.DisplayMetrics displayMetrics = new android.util.DisplayMetrics();
        windowManager = (WindowManager) getSystemService(WINDOW_SERVICE); // Initialize windowManager earlier if needed for getDefaultDisplay
        if (windowManager != null) {
            windowManager.getDefaultDisplay().getMetrics(displayMetrics);
            maxChatWidthPx = displayMetrics.widthPixels;
            maxChatHeightPx = displayMetrics.heightPixels;
        } else {
            // Fallback sensible, though windowManager should be available
            maxChatWidthPx = dpToPx(800);
            maxChatHeightPx = dpToPx(1000);
        }
        
        createNotificationChannel();
        
        // Inicializar API
        initializeAPI();
        
        // Cargar configuración
        loadSettings();
        
        // Crear burbuja flotante
        createFloatingBubble();
        
        // Iniciar como foreground service con tipo DATA_SYNC
        Log.d(TAG, "Starting foreground service with DATA_SYNC type");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            startForeground(1, createNotification(), ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC);
            Log.d(TAG, "Foreground service started with DATA_SYNC type");
        } else {
            startForeground(1, createNotification());
            Log.d(TAG, "Foreground service started (pre-Q)");
        }

        IntentFilter filter = new IntentFilter(ACTION_RESTORE_CHAT_WINDOW);
        LocalBroadcastManager.getInstance(this).registerReceiver(captureCancelReceiver, filter);
        Log.d(TAG, "Capture cancel receiver registered using LocalBroadcastManager.");
        
        // Registrar receptor para cambios de orientación
        registerReceiver(orientationChangeReceiver, new IntentFilter(Intent.ACTION_CONFIGURATION_CHANGED));
        Log.d(TAG, "Orientation change receiver registered.");
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            String action = intent.getAction();
            
            if (ACTION_IMAGE_CAPTURED_FOR_CHAT.equals(action)) {
                String screenshotPath = intent.getStringExtra("screenshot_path");
                // Leer el flag que indica si se debe guardar en la galería, independientemente de la preferencia del usuario
                boolean shouldSaveToGallery = intent.getBooleanExtra("save_to_gallery", false);
                
                if (screenshotPath != null) {
                    if (pendingCapturedImageBitmap != null && !pendingCapturedImageBitmap.isRecycled()) {
                        pendingCapturedImageBitmap.recycle();
                    }
                    pendingCapturedImageBitmap = BitmapFactory.decodeFile(screenshotPath);
                    if (pendingCapturedImageBitmap != null) {
                        Log.d(TAG, "ACTION_IMAGE_CAPTURED_FOR_CHAT: Bitmap loaded, size: " + 
                                   pendingCapturedImageBitmap.getWidth() + "x" + pendingCapturedImageBitmap.getHeight());
                        currentActiveImageForPromptButtons = pendingCapturedImageBitmap; // Set for prompt buttons
                        
                        // Si está activada la opción o se recibió el flag, guardar una copia de la imagen en la galería
                        if (saveCapturesInGallery || shouldSaveToGallery) {
                            // Crear una copia para guardar en la galería
                            Bitmap galleryBitmap = pendingCapturedImageBitmap.copy(pendingCapturedImageBitmap.getConfig(), false);
                            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss", java.util.Locale.getDefault()).format(new java.util.Date());
                            saveImageToGallery(galleryBitmap, "FloatingAI_" + timestamp);
                            // No reciclar aquí galleryBitmap ya que saveImageToGallery se encarga de eso
                        }
                        
                        showChatWindow();
                        addMessageToChat("Sistema", "️ Imagen capturada. Escribe tu mensaje o usa los botones de prompt.", false);
                        showPromptButtons(currentActiveImageForPromptButtons); // Show prompt buttons
                        if (messageInput != null) {
                            messageInput.setText("");
                        }
                        cleanupScreenshotFile(screenshotPath);
                    } else {
                        Log.e(TAG, "ACTION_IMAGE_CAPTURED_FOR_CHAT: Failed to load bitmap from path: " + screenshotPath);
                        addMessageToChat("Sistema", "❌ Error al cargar la imagen capturada.", false);
                    }
                } else {
                    Log.e(TAG, "ACTION_IMAGE_CAPTURED_FOR_CHAT: screenshot_path was null.");
                }
            } else if ("PROCESS_SCREENSHOT".equals(action)) {
                Log.w(TAG, "PROCESS_SCREENSHOT action received, but it's deprecated. Please update to ACTION_IMAGE_CAPTURED_FOR_CHAT.");
            } else if ("PROCESS_SHARED_IMAGE".equals(action)) {
                String imageUriString = intent.getStringExtra("image_uri");
                processSharedImage(imageUriString);
            }
        }
        return START_STICKY;
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "Servicio de Burbuja Flotante",
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("Mantiene la burbuja flotante activa");
            
            NotificationManager manager = getSystemService(NotificationManager.class);
            manager.createNotificationChannel(channel);
        }
    }

    private Notification createNotification() {
        return new Notification.Builder(this, CHANNEL_ID)
            .setContentTitle("FloatingAI")
            .setContentText("Burbuja flotante activa")
            .setSmallIcon(android.R.drawable.ic_dialog_info)
            .build();
    }

    private void initializeAPI() {
        HttpLoggingInterceptor logging = new HttpLoggingInterceptor();
        logging.setLevel(HttpLoggingInterceptor.Level.BODY);

        OkHttpClient client = new OkHttpClient.Builder()
            .addInterceptor(logging)
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .build();

        Retrofit retrofit = new Retrofit.Builder()
            .baseUrl("https://openrouter.ai/api/v1/")
            .client(client)
            .addConverterFactory(GsonConverterFactory.create())
            .build();

        apiService = retrofit.create(OpenRouterAPI.class);
    }

    private void loadSettings() {
        SharedPreferences prefs = getSharedPreferences("app_prefs", MODE_PRIVATE);
        apiKey = prefs.getString("api_key", "");
        selectedModel = prefs.getString("selected_model", "");

        // Load new settings
        aiTemperature = prefs.getFloat(OtherOptionsActivity.KEY_TEMPERATURE, OtherOptionsActivity.DEFAULT_TEMPERATURE);
        aiContextWindow = prefs.getInt(OtherOptionsActivity.KEY_CONTEXT_WINDOW, OtherOptionsActivity.DEFAULT_CONTEXT_WINDOW);
        aiMaxTokens = prefs.getInt(OtherOptionsActivity.KEY_MAX_TOKENS, OtherOptionsActivity.DEFAULT_MAX_TOKENS);
        chatFontSize = prefs.getString(OtherOptionsActivity.KEY_FONT_SIZE, OtherOptionsActivity.DEFAULT_FONT_SIZE);
        sendWithEnterEnabled = prefs.getBoolean(OtherOptionsActivity.KEY_SEND_WITH_ENTER, OtherOptionsActivity.DEFAULT_SEND_WITH_ENTER);
        aiSystemPrompt = prefs.getString(OtherOptionsActivity.KEY_SYSTEM_PROMPT, OtherOptionsActivity.DEFAULT_SYSTEM_PROMPT); // Load system prompt

        // Load settings for configurable prompt buttons
        promptButton1Text = prefs.getString(OtherOptionsActivity.KEY_PROMPT_BUTTON_1_TEXT, OtherOptionsActivity.DEFAULT_PROMPT_BUTTON_1_TEXT);
        promptButton1Enabled = prefs.getBoolean(OtherOptionsActivity.KEY_PROMPT_BUTTON_1_ENABLED, OtherOptionsActivity.DEFAULT_PROMPT_BUTTON_1_ENABLED);
        promptButton2Text = prefs.getString(OtherOptionsActivity.KEY_PROMPT_BUTTON_2_TEXT, OtherOptionsActivity.DEFAULT_PROMPT_BUTTON_2_TEXT);
        promptButton2Enabled = prefs.getBoolean(OtherOptionsActivity.KEY_PROMPT_BUTTON_2_ENABLED, OtherOptionsActivity.DEFAULT_PROMPT_BUTTON_2_ENABLED);
        promptButton3Text = prefs.getString(OtherOptionsActivity.KEY_PROMPT_BUTTON_3_TEXT, OtherOptionsActivity.DEFAULT_PROMPT_BUTTON_3_TEXT);
        promptButton3Enabled = prefs.getBoolean(OtherOptionsActivity.KEY_PROMPT_BUTTON_3_ENABLED, OtherOptionsActivity.DEFAULT_PROMPT_BUTTON_3_ENABLED);
        promptButton4Text = prefs.getString(OtherOptionsActivity.KEY_PROMPT_BUTTON_4_TEXT, OtherOptionsActivity.DEFAULT_PROMPT_BUTTON_4_TEXT);
        promptButton4Enabled = prefs.getBoolean(OtherOptionsActivity.KEY_PROMPT_BUTTON_4_ENABLED, OtherOptionsActivity.DEFAULT_PROMPT_BUTTON_4_ENABLED);

        closeOnOutsideEnabled = prefs.getBoolean("close_on_outside", false);
        
        // Cargar preferencia de guardar capturas en la galería
        saveCapturesInGallery = prefs.getBoolean("save_captures_in_gallery", false);

        Log.d(TAG, "Settings loaded - API key length: " + (apiKey != null ? apiKey.length() : 0) + ", Model: " + selectedModel);
        Log.d(TAG, "AI Settings - Temp: " + aiTemperature + ", Context: " + aiContextWindow + ", MaxTokens: " + aiMaxTokens + ", SystemPrompt: " + aiSystemPrompt); // Log system prompt
        Log.d(TAG, "Chat Settings - Font: " + chatFontSize + ", SendWithEnter: " + sendWithEnterEnabled);
        Log.d(TAG, "Prompt Button 1: '" + promptButton1Text + "', Enabled: " + promptButton1Enabled);
        Log.d(TAG, "Prompt Button 2: '" + promptButton2Text + "', Enabled: " + promptButton2Enabled);
        Log.d(TAG, "Prompt Button 3: '" + promptButton3Text + "', Enabled: " + promptButton3Enabled);
        Log.d(TAG, "Prompt Button 4: '" + promptButton4Text + "', Enabled: " + promptButton4Enabled);

        if (apiKey == null || apiKey.trim().isEmpty()) {
            Log.w(TAG, "API key is empty or null!");
        }
    }

    // Method to save chat window position and size
    private void saveChatWindowState() {
        if (chatParams == null) {
            return;
        }
        
        SharedPreferences prefs = getSharedPreferences("app_prefs", MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        
        editor.putInt("chat_window_x", chatParams.x);
        editor.putInt("chat_window_y", chatParams.y);
        editor.putInt("chat_window_width", chatParams.width);
        editor.putInt("chat_window_height", chatParams.height);
        editor.apply();
        
        Log.d(TAG, "Chat window state saved - X: " + chatParams.x + ", Y: " + chatParams.y + 
              ", Width: " + chatParams.width + ", Height: " + chatParams.height);
    }

    // Method to load chat window position and size
    private boolean loadChatWindowState() {
        SharedPreferences prefs = getSharedPreferences("app_prefs", MODE_PRIVATE);
        
        // Check if we have saved state
        if (!prefs.contains("chat_window_x")) {
            return false; // No saved state
        }
        
        if (chatParams == null) {
            return false;
        }
        
        chatParams.x = prefs.getInt("chat_window_x", chatParams.x);
        chatParams.y = prefs.getInt("chat_window_y", chatParams.y);
        chatParams.width = prefs.getInt("chat_window_width", chatParams.width);
        chatParams.height = prefs.getInt("chat_window_height", chatParams.height);
        
        // Validate the loaded values to ensure they're within screen bounds
        android.util.DisplayMetrics displayMetrics = new android.util.DisplayMetrics();
        windowManager.getDefaultDisplay().getMetrics(displayMetrics);
        int screenWidth = displayMetrics.widthPixels;
        int screenHeight = displayMetrics.heightPixels;
        
        // Ensure the window is not positioned completely off-screen
        chatParams.x = Math.max(0, Math.min(chatParams.x, screenWidth - minChatWidthPx));
        chatParams.y = Math.max(0, Math.min(chatParams.y, screenHeight - minChatHeightPx));
        
        // Ensure width and height are within valid bounds
        chatParams.width = Math.max(minChatWidthPx, Math.min(chatParams.width, screenWidth));
        chatParams.height = Math.max(minChatHeightPx, Math.min(chatParams.height, screenHeight));
        
        Log.d(TAG, "Chat window state loaded - X: " + chatParams.x + ", Y: " + chatParams.y + 
              ", Width: " + chatParams.width + ", Height: " + chatParams.height);
        
        return true;
    }

    // Method to save chat window state with delay to avoid excessive saving during drag/resize
    private void scheduleSaveChatWindowState() {
        if (saveStateRunnable != null) {
            saveStateHandler.removeCallbacks(saveStateRunnable);
        }
        
        saveStateRunnable = () -> {
            saveChatWindowState();
            saveStateRunnable = null;
        };
        
        saveStateHandler.postDelayed(saveStateRunnable, SAVE_STATE_DELAY);
    }

    private void createFloatingBubble() {
        windowManager = (WindowManager) getSystemService(WINDOW_SERVICE);
        
        // Crear la vista de la burbuja
        floatingBubble = LayoutInflater.from(this).inflate(R.layout.floating_button, null);
        
        // Configurar parámetros de la ventana de la burbuja
        bubbleParams = new WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        );
        bubbleParams.gravity = Gravity.TOP | Gravity.START;
        bubbleParams.x = 0;
        bubbleParams.y = 100;
        
        // Crear la vista de eliminación (X)
        dismissView = new ImageView(this);
        dismissView.setImageResource(android.R.drawable.ic_menu_close_clear_cancel); // System X icon
        // You can use a custom drawable for a better look: dismissView.setImageResource(R.drawable.my_custom_dismiss_icon);
        dismissView.setBackgroundColor(android.graphics.Color.argb(100, 255, 0, 0)); // Semi-transparent red background
        dismissView.setPadding(20, 20, 20, 20);

        dismissParams = new WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
            PixelFormat.TRANSLUCENT
        );
        dismissParams.gravity = Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL;
        dismissParams.y = 50; // Margin from bottom
        
        // Agregar la burbuja a la ventana
        windowManager.addView(floatingBubble, bubbleParams);
        
        // Configurar eventos
        setupBubbleEvents();
    }

    private void setupBubbleEvents() {
        Button bubbleButton = floatingBubble.findViewById(R.id.floating_button);

        longPressRunnable = () -> {
            Log.d(TAG, "Long press detected on bubble");
            isLongPressDetected = true;

            // Animación de feedback visual usando la variable ya existente
            bubbleButton.animate()
                .scaleX(1.2f)
                .scaleY(1.2f)
                .setDuration(150)
                .withEndAction(() -> {
                    bubbleButton.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .withEndAction(() -> {
                            // Iniciar captura después de la animación
                            captureScreenshot();
                        })
                        .start();
                })
                .start();
        };

        bubbleButton.setOnTouchListener(new View.OnTouchListener() {
            private float downX, downY;
            private static final float CLICK_DRAG_TOLERANCE = 10;

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                int dismissViewWidth = dismissView.getWidth();
                int dismissViewHeight = dismissView.getHeight();
                android.util.DisplayMetrics displayMetrics = new android.util.DisplayMetrics();
                windowManager.getDefaultDisplay().getMetrics(displayMetrics);
                int screenHeight = displayMetrics.heightPixels;
                int screenWidth = displayMetrics.widthPixels;

                int dismissAreaTop = screenHeight - dismissViewHeight - dismissParams.y - 100;
                int dismissAreaBottom = screenHeight - dismissParams.y + 50;
                int dismissAreaLeft = (screenWidth - dismissViewWidth) / 2 - 50;
                int dismissAreaRight = (screenWidth + dismissViewWidth) / 2 + 50;

                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        downX = event.getRawX();
                        downY = event.getRawY();
                        initialX = bubbleParams.x;
                        initialY = bubbleParams.y;
                        initialTouchX = event.getRawX();
                        initialTouchY = event.getRawY();
                        isLongPressDetected = false;
                        longPressHandler.postDelayed(longPressRunnable, LONG_PRESS_TIMEOUT);
                        
                        if (!isDismissViewVisible) {
                            windowManager.addView(dismissView, dismissParams);
                            isDismissViewVisible = true;
                        }
                        return true;
                        
                    case MotionEvent.ACTION_MOVE:
                        bubbleParams.x = initialX + (int) (event.getRawX() - initialTouchX);
                        bubbleParams.y = initialY + (int) (event.getRawY() - initialTouchY);
                        windowManager.updateViewLayout(floatingBubble, bubbleParams);
                        
                        // Si el movimiento es significativo, cancelar la pulsación larga
                        if (Math.abs(event.getRawX() - downX) > CLICK_DRAG_TOLERANCE ||
                            Math.abs(event.getRawY() - downY) > CLICK_DRAG_TOLERANCE) {
                            longPressHandler.removeCallbacks(longPressRunnable);
                        }

                        int bubbleCenterX = bubbleParams.x + floatingBubble.getWidth() / 2;
                        int bubbleCenterY = bubbleParams.y + floatingBubble.getHeight() / 2;

                        if (bubbleCenterY > dismissAreaTop && bubbleCenterY < dismissAreaBottom &&
                            bubbleCenterX > dismissAreaLeft && bubbleCenterX < dismissAreaRight) {
                            isBubbleOverDismissArea = true;
                            dismissView.setBackgroundColor(android.graphics.Color.argb(200, 255, 0, 0));
                        } else {
                            isBubbleOverDismissArea = false;
                            dismissView.setBackgroundColor(android.graphics.Color.argb(100, 255, 0, 0));
                        }
                        return true;
                        
                    case MotionEvent.ACTION_UP:
                        longPressHandler.removeCallbacks(longPressRunnable);
                        
                        if (isDismissViewVisible) {
                            windowManager.removeView(dismissView);
                            isDismissViewVisible = false;
                        }
                        
                        if (isBubbleOverDismissArea) {
                            stopSelf();
                            Toast.makeText(FloatingService.this, "Burbuja eliminada", Toast.LENGTH_SHORT).show();
                        } else if (!isLongPressDetected) {
                            // Si no fue pulsación larga y no se eliminó, verificar si fue un clic
                            if (Math.abs(event.getRawX() - downX) < CLICK_DRAG_TOLERANCE &&
                                Math.abs(event.getRawY() - downY) < CLICK_DRAG_TOLERANCE) {
                                handleBubbleClick();
                            }
                        }
                        isBubbleOverDismissArea = false;
                        return true;
                }
                return false;
            }
        });
    }

    private void handleBubbleClick() {
        if (isChatVisible) {
            hideChatWindow();
        } else {
            showChatWindow();
        }
    }

    private void showChatWindow() {
        Log.d(TAG, "showChatWindow() called. isChatUiInitialized = " + isChatUiInitialized + ", isChatVisible = " + isChatVisible);
        loadSettings(); // Reload settings when showing the chat window
        
        boolean stateLoaded = false; // Variable para controlar si se cargó un estado guardado
        
        if (!isChatUiInitialized) {
            Log.d(TAG, "Initializing Chat UI for the first time.");
            chatWindow = LayoutInflater.from(this).inflate(R.layout.chat_window, null);
            
            // Get screen dimensions for initial centering
            android.util.DisplayMetrics displayMetrics = new android.util.DisplayMetrics();
            windowManager.getDefaultDisplay().getMetrics(displayMetrics);
            int screenWidth = displayMetrics.widthPixels;
            int screenHeight = displayMetrics.heightPixels;

            int initialChatWidth = (int) (screenWidth * 0.8);
            int initialChatHeight = (int) (screenHeight * 0.6);

            chatParams = new WindowManager.LayoutParams(
                initialChatWidth,
                initialChatHeight,
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL | WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH, 
                PixelFormat.TRANSLUCENT
            );
            chatParams.gravity = Gravity.TOP | Gravity.START; // Changed gravity
            
            // Center the window initially (will be overridden if saved state exists)
            chatParams.x = (screenWidth - initialChatWidth) / 2;
            chatParams.y = (screenHeight - initialChatHeight) / 2;
            
            // Try to load saved window state, if it doesn't exist use default values
            stateLoaded = loadChatWindowState();
            if (!stateLoaded) {
                Log.d(TAG, "No saved window state found, using default position and size");
            }
            
            initializeChatComponents(); 

            // Temporarily commented out for diagnosing text selection CAB issue
            chatWindow.setOnTouchListener(new View.OnTouchListener() {
                // private float initialDownX, initialDownY; // Replaced by dragCheckRawX, dragCheckRawY
                private static final float CLICK_DRAG_TOLERANCE_CHAT = 20; 

                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    int viewWidth = v.getWidth();
                    int viewHeight = v.getHeight();
                    int touchXOnView = (int) event.getX();
                    int touchYOnView = (int) event.getY();

                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            isResizing = false;
                            currentResizeMode = RESIZE_MODE_NONE;

                            // Check for resize first (bottom areas)
                            if (touchXOnView < resizeHandleAreaPx && touchYOnView > viewHeight - resizeHandleAreaPx) { // Bottom-left
                                isResizing = true;
                                currentResizeMode = RESIZE_MODE_BOTTOM_LEFT;
                            } else if (touchXOnView > viewWidth - resizeHandleAreaPx && touchYOnView > viewHeight - resizeHandleAreaPx) { // Bottom-right
                                isResizing = true;
                                currentResizeMode = RESIZE_MODE_BOTTOM_RIGHT;
                            } else if (touchYOnView > viewHeight - resizeHandleAreaPx) { // Bottom edge (center part)
                                isResizing = true;
                                currentResizeMode = RESIZE_MODE_BOTTOM;
                            }

                            if (isResizing) {
                                Log.d(TAG, "ACTION_DOWN: Resizing mode: " + currentResizeMode);
                                resizeStartWidth = chatParams.width;
                                resizeStartHeight = chatParams.height;
                                resizeStartX = chatParams.x; // Store initial X for left-side resizing
                                initialTouchX_resize = event.getRawX();
                                initialTouchY_resize = event.getRawY();
                            } else {
                                // Potential drag
                                Log.d(TAG, "ACTION_DOWN: Potential drag");
                                chatInitialX = chatParams.x;
                                chatInitialY = chatParams.y;
                                initialTouchX = event.getRawX(); // For drag
                                initialTouchY = event.getRawY(); // For drag
                                dragCheckRawX = event.getRawX(); // For differentiating click vs drag
                                dragCheckRawY = event.getRawY();
                            }
                            // Si la opción está activada y el toque es fuera de la ventana, cerrar el chat
                            if (closeOnOutsideEnabled) {
                                // Detectar si el toque fue fuera de la ventana de chat
                                // Como este listener está en chatWindow, solo se activa si el toque es sobre la ventana
                                // Para detectar toques fuera, necesitamos usar FLAG_WATCH_OUTSIDE_TOUCH y ACTION_OUTSIDE
                            }
                            return true; 

                        case MotionEvent.ACTION_MOVE:
                            if (isResizing) {
                                float rawDeltaX = event.getRawX() - initialTouchX_resize;
                                float rawDeltaY = event.getRawY() - initialTouchY_resize;

                                int newWidth = chatParams.width;
                                int newHeight = chatParams.height;
                                int newX = chatParams.x;

                                switch (currentResizeMode) {
                                    case RESIZE_MODE_BOTTOM_RIGHT:
                                        newWidth = resizeStartWidth + (int) rawDeltaX;
                                        newHeight = resizeStartHeight + (int) rawDeltaY;
                                        break;
                                    case RESIZE_MODE_BOTTOM_LEFT:
                                        newWidth = resizeStartWidth - (int) rawDeltaX;
                                        newHeight = resizeStartHeight + (int) rawDeltaY;
                                        newX = resizeStartX + (int) rawDeltaX; 
                                        break;
                                    case RESIZE_MODE_BOTTOM:
                                        newHeight = resizeStartHeight + (int) rawDeltaY;
                                        break;
                                }

                                newWidth = Math.max(minChatWidthPx, Math.min(newWidth, maxChatWidthPx));
                                newHeight = Math.max(minChatHeightPx, Math.min(newHeight, maxChatHeightPx));
                                // For left resize, ensure X doesn\'t push window off-screen due to width change
                                if (currentResizeMode == RESIZE_MODE_BOTTOM_LEFT) {
                                    newX = Math.max(0, Math.min(newX, maxChatWidthPx - newWidth));
                                }


                                if (newWidth != chatParams.width || newHeight != chatParams.height || (currentResizeMode == RESIZE_MODE_BOTTOM_LEFT && newX != chatParams.x) ) {
                                    chatParams.width = newWidth;
                                    chatParams.height = newHeight;
                                    if (currentResizeMode == RESIZE_MODE_BOTTOM_LEFT) {
                                        chatParams.x = newX;
                                    }
                                    try {
                                        if (chatWindow != null && chatWindow.isAttachedToWindow()) {
                                            windowManager.updateViewLayout(chatWindow, chatParams);
                                            // Schedule saving the state after resize
                                            scheduleSaveChatWindowState();
                                        }
                                    } catch (Exception e) {
                                        Log.e(TAG, "Error updating chat window layout during resize", e);
                                    }
                                }
                            } else { // Dragging
                                if (Math.abs(event.getRawX() - dragCheckRawX) > CLICK_DRAG_TOLERANCE_CHAT ||
                                    Math.abs(event.getRawY() - dragCheckRawY) > CLICK_DRAG_TOLERANCE_CHAT) {
                                    
                                    chatParams.x = chatInitialX + (int) (event.getRawX() - initialTouchX);
                                    chatParams.y = chatInitialY + (int) (event.getRawY() - initialTouchY);
                                    try {
                                        if (chatWindow != null && chatWindow.isAttachedToWindow()) {
                                            windowManager.updateViewLayout(chatWindow, chatParams);
                                            // Schedule saving the state after drag
                                            scheduleSaveChatWindowState();
                                        }
                                    } catch (IllegalArgumentException e) {
                                        Log.e(TAG, "Error updating chat window layout during drag", e);
                                    }
                                }
                            }
                            return true; 

                        case MotionEvent.ACTION_UP:
                            if (isResizing) {
                                isResizing = false;
                                currentResizeMode = RESIZE_MODE_NONE;
                                Log.d(TAG, "ACTION_UP: Resizing finished");
                            } else {
                                // Check if it was a click (small movement)
                                if (Math.abs(event.getRawX() - dragCheckRawX) < CLICK_DRAG_TOLERANCE_CHAT &&
                                    Math.abs(event.getRawY() - dragCheckRawY) < CLICK_DRAG_TOLERANCE_CHAT) {
                                    Log.d(TAG, "ACTION_UP: Click on chat window detected (not drag or resize)");
                                    // Perform click action if any, e.g., dismiss keyboard if not on an input field
                                    // For now, just logs.
                                } else {
                                    Log.d(TAG, "ACTION_UP: Drag finished");
                                }
                            }
                            return true; 
                        case MotionEvent.ACTION_OUTSIDE:
                            if (closeOnOutsideEnabled) {
                                hideChatWindow();
                                return true;
                            }
                            return false;
                    }
                    return false; 
                }
            });
            
            
            try {
                windowManager.addView(chatWindow, chatParams);
                isChatUiInitialized = true;
                Log.d(TAG, "Chat window added to WindowManager for the first time.");
            } catch (Exception e) {
                Log.e(TAG, "Error adding chat window to WindowManager", e);
                chatWindow = null; 
                isChatUiInitialized = false; // Reset if failed
                isChatVisible = false;
                return; // Exit if failed to add
            }
        } else {
            // Chat UI is already initialized, messageInput should exist.
            // Update input behavior in case settings like "Send with Enter" changed.
            if (messageInput != null) {
                updateMessageInputBehavior();
            }
            
            // Cargar la posición y tamaño guardados cuando se vuelve a mostrar la ventana
            stateLoaded = loadChatWindowState();
            Log.d(TAG, "Loaded saved window state for existing chat window");
            
            // Aplicar el estado cargado actualizando el layout
            if (chatWindow != null && chatWindow.isAttachedToWindow() && chatParams != null) {
                try {
                    windowManager.updateViewLayout(chatWindow, chatParams);
                    Log.d(TAG, "Applied saved window state - X: " + chatParams.x + ", Y: " + chatParams.y + 
                          ", Width: " + chatParams.width + ", Height: " + chatParams.height);
                } catch (Exception e) {
                    Log.e(TAG, "Error updating chat window layout with saved state", e);
                }
            }
        }

        // If UI is initialized, make it visible (or ensure it is)
        if (chatWindow != null) {
            chatWindow.setVisibility(View.VISIBLE);
            isChatVisible = true;
            Log.d(TAG, "Made chat window visible.");

            if (messageInput != null) {
                messageInput.requestFocus();
                InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
                imm.showSoftInput(messageInput, InputMethodManager.SHOW_IMPLICIT);
                Log.d(TAG, "Requested focus and keyboard for messageInput.");
            }
            
            // Solo ajustar a orientación si no se cargó un estado guardado
            if (!stateLoaded) {
                adjustChatWindowToOrientation();
                Log.d(TAG, "Adjusting chat window to orientation (no saved state)");
            }
        } else {
            Log.e(TAG, "showChatWindow: chatWindow is null even after initialization attempt or was never initialized.");
        }
    }

    private void initializeChatComponents() {
        if (chatWindow == null) return;

        selectModelButton = chatWindow.findViewById(R.id.select_model_button); // Initialize the button
        if (selectModelButton != null) {
            selectModelButton.setOnClickListener(v -> showFavoriteModelsPopupMenu(v)); // Changed to show PopupMenu
        }

        TextView chatWindowTitle = chatWindow.findViewById(R.id.chat_window_title); // Get title TextView
        if (chatWindowTitle != null) {
            titleLongPressRunnable = () -> {
                Log.d(TAG, "Long press detected on chat window title");
                Intent intent = new Intent(FloatingService.this, MainActivity.class);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                startActivity(intent);
                if (isChatVisible) {
                    hideChatWindow();
                }
            };

            chatWindowTitle.setOnTouchListener(new View.OnTouchListener() {
                private float downX, downY;
                private static final float CLICK_DRAG_TOLERANCE_TITLE = 10; // Tolerance for title click/drag

                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            downX = event.getRawX();
                            downY = event.getRawY();
                            longPressHandler.postDelayed(titleLongPressRunnable, LONG_PRESS_TIMEOUT);
                            return true; // Consume event to receive further actions
                        case MotionEvent.ACTION_MOVE:
                            if (Math.abs(event.getRawX() - downX) > CLICK_DRAG_TOLERANCE_TITLE ||
                                Math.abs(event.getRawY() - downY) > CLICK_DRAG_TOLERANCE_TITLE) {
                                longPressHandler.removeCallbacks(titleLongPressRunnable);
                            }
                            // Allow chat window dragging logic to handle movement if not a long press
                            // This might need to be coordinated with the main chat window's onTouchListener
                            // For now, returning false allows parent to handle it, but this might need refinement
                            // depending on how you want drag and long-press on title to interact with window drag.
                            // Let's return false and see the behavior for now with the existing chat window drag.
                            // Update: For specific title interaction, we should probably consume move if a long press is pending
                            // and only pass to parent if long press is cancelled.
                            // However, the existing chat window touch listener already handles drag.
                            // The title is part of the chatWindow, so its touch events are part of the chatWindow's touch handling.
                            // We primarily care about ACTION_DOWN and ACTION_UP/ACTION_CANCEL for the long press.
                            // The existing chatWindow onTouch listener will handle the drag part.
                            // We just need to make sure the long press on title is detected.
                            return false; // Let parent handle move for dragging the window
                        case MotionEvent.ACTION_UP:
                        case MotionEvent.ACTION_CANCEL:
                            longPressHandler.removeCallbacks(titleLongPressRunnable);
                            return false; // Let parent handle up/cancel
                    }
                    return false;
                }
            });
        }

        chatHistory = chatWindow.findViewById(R.id.chat_history);
        messageInput = chatWindow.findViewById(R.id.message_input);
        chatScrollView = chatWindow.findViewById(R.id.chat_scroll);
        
        Button sendButton = chatWindow.findViewById(R.id.send_button);
        Button screenshotButton = chatWindow.findViewById(R.id.screenshot_button);
        Button galleryButton = chatWindow.findViewById(R.id.gallery_button);
        ImageButton closeButton = chatWindow.findViewById(R.id.close_button);
        ImageButton newChatButton = chatWindow.findViewById(R.id.new_chat_button);
        // Get reference to the prompt buttons container
        promptButtonsContainer = chatWindow.findViewById(R.id.prompt_buttons_container);

        // Initialize CLICK_DRAG_TOLERANCE_BUTTON in pixels
        final float clickDragToleranceButtonPx = dpToPx((int)CLICK_DRAG_TOLERANCE_BUTTON);

        startDragRunnable = () -> {
            if (isPotentialTapOnNewChatButton) { // Check if still a candidate for drag start
                isDraggingChatWindowViaNewChatButton = true;
                isPotentialTapOnNewChatButton = false; // No longer a tap
                Log.d(TAG, "Drag started via new chat button after delay");
                // Optionally, provide haptic feedback for drag start
                // v.performHapticFeedback(android.view.HapticFeedbackConstants.LONG_PRESS);
            }
        };

        if (newChatButton != null) {
            // Remove previous OnClickListener if any, and set OnTouchListener
            newChatButton.setOnClickListener(null); 
            newChatButton.setOnTouchListener((v, event) -> {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        newChatButtonDownRawX = event.getRawX();
                        newChatButtonDownRawY = event.getRawY();
                        // Capture window's current X & Y for dragging
                        // These are already member variables, ensure they are correctly set if chatParams can be null initially
                        if (chatParams != null) {
                            chatInitialX = chatParams.x; 
                            chatInitialY = chatParams.y;
                        } else {
                            // Fallback or log error if chatParams is unexpectedly null
                            Log.e(TAG, "newChatButton ACTION_DOWN: chatParams is null!");
                            // You might want to initialize them to current window position if possible
                            // or prevent interaction if state is invalid. For now, assuming chatParams is valid.
                            // If showChatWindow always initializes chatParams before this can be touched, it's fine.
                        }
                        
                        isPotentialTapOnNewChatButton = true;
                        isDraggingChatWindowViaNewChatButton = false;
                        newChatButtonHandler.postDelayed(startDragRunnable, START_DRAG_DELAY);
                        v.setPressed(true); // Show pressed state
                        return true; // Consume event

                    case MotionEvent.ACTION_MOVE:
                        if (isDraggingChatWindowViaNewChatButton || (isPotentialTapOnNewChatButton &&
                            (Math.abs(event.getRawX() - newChatButtonDownRawX) > clickDragToleranceButtonPx ||
                             Math.abs(event.getRawY() - newChatButtonDownRawY) > clickDragToleranceButtonPx))) {

                            if (isPotentialTapOnNewChatButton) { 
                                newChatButtonHandler.removeCallbacks(startDragRunnable);
                                isPotentialTapOnNewChatButton = false;
                                isDraggingChatWindowViaNewChatButton = true;
                                Log.d(TAG, "Drag started via new chat button due to movement");
                                // Potentially cancel pressed state if drag starts immediately
                                // v.setPressed(false); 
                            }

                            if (isDraggingChatWindowViaNewChatButton && chatParams != null) {
                                chatParams.x = chatInitialX + (int) (event.getRawX() - newChatButtonDownRawX);
                                chatParams.y = chatInitialY + (int) (event.getRawY() - newChatButtonDownRawY);
                                try {
                                    if (chatWindow != null && chatWindow.isAttachedToWindow()) {
                                        windowManager.updateViewLayout(chatWindow, chatParams);
                                        // Schedule saving the state after drag
                                        scheduleSaveChatWindowState();
                                    }
                                } catch (Exception e) { // Catch generic Exception for safety
                                    Log.e(TAG, "Error updating chat window layout during drag via button", e);
                                }
                            }
                        }
                        return true; // Consume event

                    case MotionEvent.ACTION_UP:
                        v.setPressed(false); // Clear pressed state
                        newChatButtonHandler.removeCallbacks(startDragRunnable);
                        if (isPotentialTapOnNewChatButton) {
                            Log.d(TAG, "Tap detected on new chat button");
                            // Perform the "new chat" action
                            startNewConversationInternal();
                            if (chatHistory != null) {
                                chatHistory.removeAllViews();
                            }
                            if (promptButtonsContainer != null) { 
                                promptButtonsContainer.removeAllViews();
                                promptButtonsContainer.setVisibility(View.GONE);
                            }
                            addMessageToChat("FloatingAI", "¡Hola! Nueva conversación iniciada.", false);
                            Toast.makeText(FloatingService.this, "Nueva conversación iniciada", Toast.LENGTH_SHORT).show();
                            // Perform click for accessibility or other listeners if needed
                            v.performClick(); 
                        } else if (isDraggingChatWindowViaNewChatButton) {
                            Log.d(TAG, "Drag finished via new chat button");
                        }
                        isPotentialTapOnNewChatButton = false;
                        isDraggingChatWindowViaNewChatButton = false;
                        return true; // Consume event

                    case MotionEvent.ACTION_CANCEL:
                        v.setPressed(false); // Clear pressed state
                        newChatButtonHandler.removeCallbacks(startDragRunnable);
                        isPotentialTapOnNewChatButton = false;
                        isDraggingChatWindowViaNewChatButton = false;
                        Log.d(TAG, "Touch cancelled on new chat button");
                        return true; // Consume event
                }
                return false; 
            });
        }
        
        // Logic for selectModelButton
        startDragSelectModelRunnable = () -> {
            if (isPotentialTapOnSelectModelButton) {
                isDraggingChatWindowViaSelectModelButton = true;
                isPotentialTapOnSelectModelButton = false;
                Log.d(TAG, "Drag started via select_model_button after delay");
            }
        };

        if (selectModelButton != null) {
            selectModelButton.setOnClickListener(null); // Remove existing listener
            selectModelButton.setOnTouchListener((v, event) -> {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        selectModelButtonDownRawX = event.getRawX();
                        selectModelButtonDownRawY = event.getRawY();
                        if (chatParams != null) {
                            chatInitialX = chatParams.x;
                            chatInitialY = chatParams.y;
                        } else {
                             Log.e(TAG, "selectModelButton ACTION_DOWN: chatParams is null!");
                        }
                        isPotentialTapOnSelectModelButton = true;
                        isDraggingChatWindowViaSelectModelButton = false;
                        selectModelButtonHandler.postDelayed(startDragSelectModelRunnable, START_DRAG_DELAY);
                        v.setPressed(true);
                        return true;

                    case MotionEvent.ACTION_MOVE:
                        if (isDraggingChatWindowViaSelectModelButton || (isPotentialTapOnSelectModelButton &&
                            (Math.abs(event.getRawX() - selectModelButtonDownRawX) > clickDragToleranceButtonPx ||
                             Math.abs(event.getRawY() - selectModelButtonDownRawY) > clickDragToleranceButtonPx))) {
                            
                            if (isPotentialTapOnSelectModelButton) {
                                selectModelButtonHandler.removeCallbacks(startDragSelectModelRunnable);
                                isPotentialTapOnSelectModelButton = false;
                                isDraggingChatWindowViaSelectModelButton = true;
                                Log.d(TAG, "Drag started via select_model_button due to movement");
                            }

                            if (isDraggingChatWindowViaSelectModelButton && chatParams != null) {
                                chatParams.x = chatInitialX + (int) (event.getRawX() - selectModelButtonDownRawX);
                                chatParams.y = chatInitialY + (int) (event.getRawY() - selectModelButtonDownRawY);
                                try {
                                    if (chatWindow != null && chatWindow.isAttachedToWindow()) {
                                        windowManager.updateViewLayout(chatWindow, chatParams);
                                        // Schedule saving the state after drag
                                        scheduleSaveChatWindowState();
                                    }
                                } catch (Exception e) {
                                    Log.e(TAG, "Error updating chat window layout during drag via select_model_button", e);
                                }
                            }
                        }
                        return true;

                    case MotionEvent.ACTION_UP:
                        v.setPressed(false);
                        selectModelButtonHandler.removeCallbacks(startDragSelectModelRunnable);
                        if (isPotentialTapOnSelectModelButton) {
                            Log.d(TAG, "Tap detected on select_model_button");
                            showFavoriteModelsPopupMenu(v); // Original tap action
                            v.performClick(); 
                        } else if (isDraggingChatWindowViaSelectModelButton) {
                            Log.d(TAG, "Drag finished via select_model_button");
                        }
                        isPotentialTapOnSelectModelButton = false;
                        isDraggingChatWindowViaSelectModelButton = false;
                        return true;

                    case MotionEvent.ACTION_CANCEL:
                        v.setPressed(false);
                        selectModelButtonHandler.removeCallbacks(startDragSelectModelRunnable);
                        isPotentialTapOnSelectModelButton = false;
                        isDraggingChatWindowViaSelectModelButton = false;
                        Log.d(TAG, "Touch cancelled on select_model_button");
                        return true;
                }
                return false;
            });
        }

        // Logic for closeButton
        startDragCloseButtonRunnable = () -> {
            if (isPotentialTapOnCloseButton) {
                isDraggingChatWindowViaCloseButton = true;
                isPotentialTapOnCloseButton = false;
                Log.d(TAG, "Drag started via close_button after delay");
            }
        };

        if (closeButton != null) {
            closeButton.setOnClickListener(null); // Remove existing listener
            closeButton.setOnTouchListener((v, event) -> {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        closeButtonDownRawX = event.getRawX();
                        closeButtonDownRawY = event.getRawY();
                        if (chatParams != null) {
                            chatInitialX = chatParams.x;
                            chatInitialY = chatParams.y;
                        } else {
                            Log.e(TAG, "closeButton ACTION_DOWN: chatParams is null!");
                        }
                        isPotentialTapOnCloseButton = true;
                        isDraggingChatWindowViaCloseButton = false;
                        closeButtonHandler.postDelayed(startDragCloseButtonRunnable, START_DRAG_DELAY);
                        v.setPressed(true);
                        return true;

                    case MotionEvent.ACTION_MOVE:
                        if (isDraggingChatWindowViaCloseButton || (isPotentialTapOnCloseButton &&
                            (Math.abs(event.getRawX() - closeButtonDownRawX) > clickDragToleranceButtonPx ||
                             Math.abs(event.getRawY() - closeButtonDownRawY) > clickDragToleranceButtonPx))) {

                            if (isPotentialTapOnCloseButton) {
                                closeButtonHandler.removeCallbacks(startDragCloseButtonRunnable);
                                isPotentialTapOnCloseButton = false;
                                isDraggingChatWindowViaCloseButton = true;
                                Log.d(TAG, "Drag started via close_button due to movement");
                            }

                            if (isDraggingChatWindowViaCloseButton && chatParams != null) {
                                chatParams.x = chatInitialX + (int) (event.getRawX() - closeButtonDownRawX);
                                chatParams.y = chatInitialY + (int) (event.getRawY() - closeButtonDownRawY);
                                try {
                                    if (chatWindow != null && chatWindow.isAttachedToWindow()) {
                                        windowManager.updateViewLayout(chatWindow, chatParams);
                                        // Schedule saving the state after drag
                                        scheduleSaveChatWindowState();
                                    }
                                } catch (Exception e) {
                                    Log.e(TAG, "Error updating chat window layout during drag via close_button", e);
                                }
                            }
                        }
                        return true;

                    case MotionEvent.ACTION_UP:
                        v.setPressed(false);
                        closeButtonHandler.removeCallbacks(startDragCloseButtonRunnable);
                        if (isPotentialTapOnCloseButton) {
                            Log.d(TAG, "Tap detected on close_button");
                            hideChatWindow(); // Original tap action
                            v.performClick();
                        } else if (isDraggingChatWindowViaCloseButton) {
                            Log.d(TAG, "Drag finished via close_button");
                        }
                        isPotentialTapOnCloseButton = false;
                        isDraggingChatWindowViaCloseButton = false;
                        return true;

                    case MotionEvent.ACTION_CANCEL:
                        v.setPressed(false);
                        closeButtonHandler.removeCallbacks(startDragCloseButtonRunnable);
                        isPotentialTapOnCloseButton = false;
                        isDraggingChatWindowViaCloseButton = false;
                        Log.d(TAG, "Touch cancelled on close_button");
                        return true;
                }
                return false;
            });
        }
        
        // New logic for chat_window_title dragging
        startDragTitleRunnable = () -> {
            if (isPotentialTapOnTitle) {
                isDraggingChatWindowViaTitle = true;
                isPotentialTapOnTitle = false;
                Log.d(TAG, "Drag started via title after delay");
            }
        };

        if (chatWindowTitle != null) {
            chatWindowTitle.setOnClickListener(null); // Remove any existing OnClickListener
            chatWindowTitle.setOnTouchListener((v, event) -> {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        titleDownRawX = event.getRawX();
                        titleDownRawY = event.getRawY();
                        if (chatParams != null) {
                            chatInitialX = chatParams.x;
                            chatInitialY = chatParams.y;
                        } else {
                            Log.e(TAG, "chatWindowTitle ACTION_DOWN: chatParams is null!");
                        }
                        isPotentialTapOnTitle = true;
                        isDraggingChatWindowViaTitle = false;
                        titleHandler.postDelayed(startDragTitleRunnable, START_DRAG_DELAY);
                        // v.setPressed(true); // No visual pressed state for title usually
                        return true; // Consume event

                    case MotionEvent.ACTION_MOVE:
                        if (isDraggingChatWindowViaTitle || (isPotentialTapOnTitle &&
                            (Math.abs(event.getRawX() - titleDownRawX) > clickDragToleranceButtonPx || // Reuse button tolerance
                             Math.abs(event.getRawY() - titleDownRawY) > clickDragToleranceButtonPx))) {

                            if (isPotentialTapOnTitle) {
                                titleHandler.removeCallbacks(startDragTitleRunnable);
                                isPotentialTapOnTitle = false;
                                isDraggingChatWindowViaTitle = true;
                                Log.d(TAG, "Drag started via title due to movement");
                            }

                            if (isDraggingChatWindowViaTitle && chatParams != null) {
                                chatParams.x = chatInitialX + (int) (event.getRawX() - titleDownRawX);
                                chatParams.y = chatInitialY + (int) (event.getRawY() - titleDownRawY);
                                try {
                                    if (chatWindow != null && chatWindow.isAttachedToWindow()) {
                                        windowManager.updateViewLayout(chatWindow, chatParams);
                                        // Schedule saving the state after drag
                                        scheduleSaveChatWindowState();
                                    }
                                } catch (Exception e) {
                                    Log.e(TAG, "Error updating chat window layout during drag via title", e);
                                }
                            }
                        }
                        return true; // Consume event

                    case MotionEvent.ACTION_UP:
                        // v.setPressed(false);
                        titleHandler.removeCallbacks(startDragTitleRunnable);
                        if (isPotentialTapOnTitle) {
                            Log.d(TAG, "Tap detected on title. Opening MainActivity.");
                            Intent intent = new Intent(FloatingService.this, MainActivity.class);
                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                            startActivity(intent);
                            if (isChatVisible) { // Optionally hide chat window after navigating
                                hideChatWindow();
                            }
                            v.performClick(); // For accessibility
                        } else if (isDraggingChatWindowViaTitle) {
                            Log.d(TAG, "Drag finished via title");
                        }
                        isPotentialTapOnTitle = false;
                        isDraggingChatWindowViaTitle = false;
                        return true; // Consume event

                    case MotionEvent.ACTION_CANCEL:
                        // v.setPressed(false);
                        titleHandler.removeCallbacks(startDragTitleRunnable);
                        isPotentialTapOnTitle = false;
                        isDraggingChatWindowViaTitle = false;
                        Log.d(TAG, "Touch cancelled on title");
                        return true; // Consume event
                }
                return false;
            });
        }
        
        // Configure messageInput for dynamic height
        if (messageInput != null) {
            messageInput.setMaxLines(MAX_INPUT_LINES);
            messageInput.setHorizontallyScrolling(false); // Ensure text wraps

            messageInput.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    if (hasFocus) {
                        Log.d(TAG, "messageInput gained focus, reapplying input behavior.");
                        // Reload settings just in case they changed while chat was not focused
                        loadSettings(); 
                        updateMessageInputBehavior();
                    }
                }
            });

            messageInput.post(() -> {
                if (messageInput == null) return; // Guard against null if view is destroyed
                if (singleLineInputHeight == -1) {
                    int paddingTop = messageInput.getPaddingTop();
                    int paddingBottom = messageInput.getPaddingBottom();
                    // Ensure getLineHeight() is available, might need a slight delay or a default.
                    // For robustness, ensure it is > 0
                    int lineHeight = messageInput.getLineHeight();
                    if (lineHeight <= 0) { // Fallback if lineHeight not available yet
                        // Estimate based on text size or use a fixed dp value converted to px
                        // For now, let's assume it will be available. If issues, revisit.
                         lineHeight = (int) (messageInput.getTextSize() * 1.2f); // A common approximation
                    }
                    singleLineInputHeight = lineHeight + paddingTop + paddingBottom;
                }

                ViewGroup.LayoutParams params = messageInput.getLayoutParams();
                if (singleLineInputHeight > 0 && params.height != singleLineInputHeight) {
                    params.height = singleLineInputHeight;
                    messageInput.setLayoutParams(params);
                }
            });

            messageInput.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {}

                @Override
                public void afterTextChanged(Editable s) {
                    if (messageInput == null) return;
                    ViewGroup.LayoutParams params = messageInput.getLayoutParams();
                    if (s.toString().isEmpty()) {
                        // Reset to single line height if text is empty
                        if (singleLineInputHeight > 0 && params.height != singleLineInputHeight) {
                            params.height = singleLineInputHeight;
                            messageInput.setLayoutParams(params);
                            messageInput.setScrollY(0); // Reset scroll position
                        }
                    } else {
                        // Allow to grow with content
                        // WRAP_CONTENT will respect setMaxLines
                        if (params.height != ViewGroup.LayoutParams.WRAP_CONTENT) {
                           params.height = ViewGroup.LayoutParams.WRAP_CONTENT;
                           messageInput.setLayoutParams(params);
                        }
                        // Ensure the cursor/last line is visible if it grows
                        // This is a bit aggressive, might be better to let EditText handle its scrolling
                        // messageInput.setScrollY(0); // Let's remove this for now, may not be needed with WRAP_CONTENT
                    }
                }
            });
        }
        
        // Send with Enter functionality
        updateMessageInputBehavior(); // Call the new centralized method

        sendButton.setOnClickListener(v -> sendTextMessage());
        screenshotButton.setOnClickListener(v -> captureScreenshot());
        galleryButton.setOnClickListener(v -> openGallery());
        closeButton.setOnClickListener(v -> hideChatWindow());

        if (newChatButton != null) {
            newChatButton.setOnClickListener(v -> {
                // Directly start a new conversation without dialog
                startNewConversationInternal();
                // Clear the UI and add welcome message
                if (chatHistory != null) {
                    chatHistory.removeAllViews();
                }
                if (promptButtonsContainer != null) { // Clear prompt buttons on new chat
                    promptButtonsContainer.removeAllViews();
                    promptButtonsContainer.setVisibility(View.GONE);
                }
                addMessageToChat("FloatingAI", "¡Hola! Nueva conversación iniciada.", false);
                Toast.makeText(FloatingService.this, "Nueva conversación iniciada", Toast.LENGTH_SHORT).show();
            });
        }
        
        chatHistory.removeAllViews();
        boolean historyLoaded = loadChatHistory(); 
        
        if (!historyLoaded) {
            addMessageToChat("FloatingAI", "¡Hola! Puedes hacerme preguntas o seleccionar una imagen de tu galería para analizarla.", false);
            Log.d(TAG, "Welcome message added as no history was found.");
        } else {
            Log.d(TAG, "Chat history loaded.");
        }
    }

    private void updateMessageInputBehavior() {
        if (messageInput == null) {
            Log.w(TAG, "updateMessageInputBehavior: messageInput is null, cannot apply settings.");
            return;
        }

        Log.d(TAG, "updateMessageInputBehavior: Applying SendWithEnter = " + sendWithEnterEnabled);
        if (sendWithEnterEnabled) {
            messageInput.setImeOptions(android.view.inputmethod.EditorInfo.IME_ACTION_SEND);
            // Remove TYPE_TEXT_FLAG_MULTI_LINE to prioritize sending on Enter
            messageInput.setInputType(android.text.InputType.TYPE_CLASS_TEXT | android.text.InputType.TYPE_TEXT_FLAG_CAP_SENTENCES); 
            messageInput.setOnEditorActionListener((textView, actionId, keyEvent) -> {
                if (actionId == android.view.inputmethod.EditorInfo.IME_ACTION_SEND) {
                    sendTextMessage();
                    return true; // Consume the event
                }
                // If TYPE_TEXT_FLAG_MULTI_LINE is removed, Enter (even with Shift) should trigger send.
                // The isShiftPressed() check is no longer for creating new lines here.
                if (keyEvent != null && keyEvent.getKeyCode() == android.view.KeyEvent.KEYCODE_ENTER) {
                    if (keyEvent.getAction() == android.view.KeyEvent.ACTION_DOWN) {
                        // With no MULTI_LINE flag, Shift+Enter won't create a new line.
                        // So, any Enter press should attempt to send.
                        sendTextMessage();
                        return true; // Consume the event
                    }
                }
                return false; // Let the system handle other events
            });
        } else {
            // Restore default behavior: Enter creates new line
            messageInput.setImeOptions(android.view.inputmethod.EditorInfo.IME_ACTION_UNSPECIFIED);
            messageInput.setInputType(android.text.InputType.TYPE_CLASS_TEXT | android.text.InputType.TYPE_TEXT_FLAG_MULTI_LINE | android.text.InputType.TYPE_TEXT_FLAG_CAP_SENTENCES);
            messageInput.setOnEditorActionListener(null); // Remove any existing listener
        }
    }

    private boolean loadChatHistory() {
        Log.d(TAG, "loadChatHistory() called. Current conversation ID: " + currentConversationId);
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        Cursor cursor = null;
        boolean hasHistoryForCurrentConversation = false;

        if (currentConversationId == null) {
            // Try to load the most recent conversation ID
            Log.d(TAG, "currentConversationId is null, trying to load most recent one.");
            try {
                cursor = db.query(
                        ChatHistoryDbHelper.ConversationEntry.TABLE_NAME,
                        new String[]{ChatHistoryDbHelper.ConversationEntry._ID},
                        null, null, null, null,
                        ChatHistoryDbHelper.ConversationEntry.COLUMN_NAME_START_TIMESTAMP + " DESC",
                        "1"
                );
                if (cursor != null && cursor.moveToFirst()) {
                    currentConversationId = cursor.getLong(cursor.getColumnIndexOrThrow(ChatHistoryDbHelper.ConversationEntry._ID));
                    Log.d(TAG, "Loaded most recent conversation ID: " + currentConversationId);
                } else {
                    // No conversations exist, start a new one
                    Log.d(TAG, "No existing conversations found. Starting a new one.");
                    startNewConversationInternal(); 
                    if (currentConversationId == null) {
                        Log.e(TAG, "Failed to start new conversation even when none existed.");
                        return false; 
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "Error loading most recent conversation ID", e);
                startNewConversationInternal(); 
                if (currentConversationId == null) {
                    Log.e(TAG, "Failed to start new conversation after error loading recent.");
                    return false;
                }
            } finally {
                if (cursor != null) {
                    cursor.close();
                    cursor = null; 
                }
            }
        }

        if (currentConversationId == null) {
            Log.e(TAG, "Critical: currentConversationId is still null after load/create logic.");
            return false; 
        }

        Log.d(TAG, "Loading messages for conversation ID: " + currentConversationId);
        String[] projection = {
                ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_SENDER,
                ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_MESSAGE_CONTENT
        };
        String selection = ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_CONVERSATION_ID + " = ?";
        String[] selectionArgs = { String.valueOf(currentConversationId) };
        String sortOrder = ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_TIMESTAMP + " ASC";

        chatHistory.removeAllViews(); // Clear out old messages before loading new ones

        try {
            cursor = db.query(
                    ChatHistoryDbHelper.MessageEntry.TABLE_NAME,
                    projection,
                    selection, selectionArgs, null, null,
                    sortOrder
            );

            if (cursor != null) {
                java.util.List<OpenRouterRequest.Message> tempHistory = new java.util.ArrayList<>();
                while (cursor.moveToNext()) {
                    hasHistoryForCurrentConversation = true;
                    String sender = cursor.getString(cursor.getColumnIndexOrThrow(ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_SENDER));
                    String message = cursor.getString(cursor.getColumnIndexOrThrow(ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_MESSAGE_CONTENT));
                    // Add message to UI without saving it back to DB
                    addMessageToChatUI(sender, message, false); 
                }
            }
            if (hasHistoryForCurrentConversation) {
                chatScrollView.post(() -> chatScrollView.fullScroll(View.FOCUS_DOWN));
                Log.d(TAG, "Messages loaded for conversation " + currentConversationId);
            } else {
                Log.d(TAG, "No messages found for conversation " + currentConversationId + ". A welcome message might be added.");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error loading chat history from DB for conversation " + currentConversationId, e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return hasHistoryForCurrentConversation;
    }

    private void hideChatWindow() {
        Log.d(TAG, "hideChatWindow() called. isChatUiInitialized = " + isChatUiInitialized);
        if (isChatUiInitialized && chatWindow != null && chatWindow.getVisibility() == View.VISIBLE) {
            // Save the current window state before hiding
            saveChatWindowState();
            
            chatWindow.setVisibility(View.GONE);
            isChatVisible = false;
            Log.d(TAG, "Chat window set to GONE.");
        } else {
            Log.d(TAG, "Chat window was already hidden, null, or UI not initialized.");
        }
    }

    private void sendTextMessage() {
        String message = messageInput.getText().toString().trim();
        if (message.isEmpty() && pendingCapturedImageBitmap == null && currentSharedImage == null) {
            // No message and no pending/shared image, do nothing
            Toast.makeText(this, "Escribe un mensaje o adjunta una imagen.", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // Clear prompt buttons as a message is being sent
        if (promptButtonsContainer != null) {
            promptButtonsContainer.removeAllViews();
            promptButtonsContainer.setVisibility(View.GONE);
        }

        // Limpiar input
        messageInput.setText("");

        // Reset messageInput height to single line after sending
        if (messageInput != null && singleLineInputHeight > 0) {
            ViewGroup.LayoutParams params = messageInput.getLayoutParams();
            if (params.height != singleLineInputHeight) {
                params.height = singleLineInputHeight;
                messageInput.setLayoutParams(params);
            }
            messageInput.setScrollY(0); // Reset scroll position
        }
        
        if (pendingCapturedImageBitmap != null) {
            // Prioritize sending pending captured image
            addMessageToChat("Tú", message + " [con imagen capturada]");
            sendToAI(message, pendingCapturedImageBitmap);
            pendingCapturedImageBitmap.recycle(); // Recycle after use
            pendingCapturedImageBitmap = null;    // Clear after use
            currentSharedImage = null; // Clear shared image too, as captured takes precedence and is one-off
        } else if (currentSharedImage != null) {
            // Enviar con imagen compartida si no hay una capturada pendiente
            addMessageToChat("Tú", message + " [con imagen compartida]");
            sendToAI(message, currentSharedImage);
            currentSharedImage.recycle(); // Recycle after use
            currentSharedImage = null;    // Clear after use
        } else {
            // Enviar solo texto
            addMessageToChat("Tú", message);
            sendToAI(message, null);
        }
    }

    private void captureScreenshot() {
        Log.d(TAG, "captureScreenshot called in FloatingService");

        if (!ScreenCaptureAccessibilityService.isServiceEnabled()) {
            addMessageToChat("Sistema", "🔧 Habilitar Servicio de Accesibilidad:\n" +
                                       "1. Ir a Configuración > Accesibilidad\n" +
                                       "2. Buscar 'FloatingAI' y activar\n" +
                                       "Permite capturas directas sin permisos repetidos.", false);
            openAccessibilitySettings();
            return;
        }

        // Mostrar feedback visual de que se está procesando
        Toast.makeText(this, "📸 Preparando captura...", Toast.LENGTH_SHORT).show();
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            ScreenCaptureAccessibilityService service = ScreenCaptureAccessibilityService.getInstance();
            if (service != null) {
                String userMessage = messageInput != null ? messageInput.getText().toString().trim() : "";
                
                Log.d(TAG, "Before setting wasChatVisibleBeforeCapture: isChatVisible = " + isChatVisible);
                wasChatVisibleBeforeCapture = isChatVisible; // Store current state

                if (isChatVisible) {
                    Log.d(TAG, "Hiding chat window before capture. Was visible: " + wasChatVisibleBeforeCapture);
                    hideChatWindow();

                    new Handler(Looper.getMainLooper()).postDelayed(() -> {
                        Log.d(TAG, "Starting screen capture after 200ms window hide delay.");
                        service.captureScreen(userMessage);
                    }, 200); // Changed to 200
                } else {
                    Log.d(TAG, "Chat window not visible, proceeding with capture. Was visible: " + wasChatVisibleBeforeCapture);
                    service.captureScreen(userMessage);
                }
            } else {
                addMessageToChat("Sistema", "❌ Error: Servicio de accesibilidad no está listo.", false);
            }
        } else {
            addMessageToChat("Sistema", "❌ Captura por accesibilidad requiere Android 11+.", false);
        }
    }

    private void openGallery() {
        Log.d(TAG, "openGallery called");
        
        addMessageToChat("Sistema", "📂 Selección de Galería\n\n" +
                                   "Para analizar una imagen de tu galería:\n\n" +
                                   "1. 🔄 Minimiza esta ventana\n" +
                                   "2. 📂 Abre tu aplicación de Galería\n" +
                                   "3. 🖼️ Selecciona la imagen que quieres analizar\n" +
                                   "4. 📤 Usa 'Compartir' y selecciona FloatingAI\n" +
                                   "5. ✍️ Escribe tu pregunta sobre la imagen\n\n" +
                                   "💡 La imagen se analizará automáticamente con tu pregunta.", false);
        
        // Limpiar el input
        if (messageInput != null) {
            messageInput.setText("");
        }
    }

    private void processScreenshot(String userMessage, String screenshotPath) {
        Log.d(TAG, "Processing screenshot - Message: " + userMessage + ", Path: " + screenshotPath);
        
        if (!isChatVisible && floatingBubble != null) {
            Log.d(TAG, "Showing chat window for screenshot processing.");
            showChatWindow(); 
        }
        
        // Ahora que la ventana está (o debería estar) visible, añadir mensaje de procesamiento
        addMessageToChat("Sistema", "🖼️ Imagen capturada. Procesando...");

        Bitmap screenshotBitmap = null;
        if (screenshotPath != null) {
            screenshotBitmap = BitmapFactory.decodeFile(screenshotPath);
            if (screenshotBitmap != null) {
                Log.d(TAG, "Bitmap loaded successfully for processing from path: " + screenshotPath);
                
                // El mensaje del usuario ya fue capturado antes de ocultar la ventana.
                // Si el userMessage viene vacío de la captura (porque el input estaba vacío al iniciar),
                // usamos un mensaje genérico.
                String displayMessage = (userMessage != null && !userMessage.trim().isEmpty()) ? 
                                       userMessage : "Analiza esta imagen";

                addMessageToChat("Tú", displayMessage + " [con imagen capturada]");
                
                if(messageInput != null) {
                    messageInput.setText(""); // Limpiar el input después de usar el mensaje
                }

            } else {
                Log.e(TAG, "Failed to load bitmap from path: " + screenshotPath);
                addMessageToChat("Sistema", "❌ Error al cargar la imagen capturada.", false);
            }
            cleanupScreenshotFile(screenshotPath);
        } else {
             addMessageToChat("Sistema", "❌ Error: no se recibió la ruta de la imagen capturada.", false);
        }
        
        if (screenshotBitmap != null) {
            String finalMessageForAI = (userMessage != null && !userMessage.trim().isEmpty()) ? 
                                     userMessage : "Analiza esta imagen";
            Log.d(TAG, "Sending to AI - Message: " + finalMessageForAI + ", Has image: true");
            sendToAI(finalMessageForAI, screenshotBitmap);
        } 
        // No es necesario un else aquí, ya que los errores se manejan arriba.
    }

    private void processSharedImage(String imageUriString) {
        Log.d(TAG, "Processing shared image: " + imageUriString);
        
        try {
            Uri imageUri = Uri.parse(imageUriString);
            
            if (!isChatVisible) {
                showChatWindow();
            }
            
            ContentResolver contentResolver = getContentResolver();
            Bitmap imageBitmap = BitmapFactory.decodeStream(contentResolver.openInputStream(imageUri));
            
            if (imageBitmap != null) {
                Log.d(TAG, "Shared image loaded successfully - Size: " + imageBitmap.getWidth() + "x" + imageBitmap.getHeight());
                
                addMessageToChat("Sistema", "🖼️ Imagen recibida desde galería.\n" +
                                           "✍️ Escribe tu pregunta sobre la imagen y presiona enviar para analizarla, o usa los botones de prompt.", false);

                currentSharedImage = imageBitmap;
                currentActiveImageForPromptButtons = currentSharedImage; // Set for prompt buttons
                showPromptButtons(currentActiveImageForPromptButtons); // Show prompt buttons

            } else {
                Log.e(TAG, "Failed to load shared image");
                addMessageToChat("Sistema", "❌ Error al cargar la imagen compartida.", false);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error processing shared image", e);
            addMessageToChat("Sistema", "❌ Error al procesar la imagen: " + e.getMessage(), false);
        }
    }

    private void cleanupScreenshotFile(String filePath) {
        try {
            File file = new File(filePath);
            if (file.exists()) {
                file.delete();
                Log.d(TAG, "Screenshot file deleted: " + filePath);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error deleting screenshot file", e);
        }
    }

    /**
     * Guarda una imagen en la galería del dispositivo.
     * @param bitmap La imagen a guardar
     * @param title El título para la imagen
     * @return true si se guardó exitosamente, false de lo contrario
     */
    private boolean saveImageToGallery(Bitmap bitmap, String title) {
        if (bitmap == null) {
            Log.e(TAG, "saveImageToGallery: bitmap is null");
            return false;
        }

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Para Android 10 y superior, usamos MediaStore API
                android.content.ContentValues values = new android.content.ContentValues();
                values.put(android.provider.MediaStore.Images.Media.DISPLAY_NAME, title + ".jpg");
                values.put(android.provider.MediaStore.Images.Media.MIME_TYPE, "image/jpeg");
                values.put(android.provider.MediaStore.Images.Media.RELATIVE_PATH, android.os.Environment.DIRECTORY_PICTURES + "/Screenshots");
                values.put(android.provider.MediaStore.Images.Media.IS_PENDING, 1);

                ContentResolver contentResolver = getContentResolver();
                Uri imageUri = contentResolver.insert(android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values);

                if (imageUri != null) {
                    try (java.io.OutputStream outputStream = contentResolver.openOutputStream(imageUri)) {
                        bitmap.compress(Bitmap.CompressFormat.JPEG, 95, outputStream);
                    }

                    values.clear();
                    values.put(android.provider.MediaStore.Images.Media.IS_PENDING, 0);
                    contentResolver.update(imageUri, values, null, null);
                    
                    Log.d(TAG, "Image saved to gallery: " + title);
                    Toast.makeText(this, "Imagen guardada en la galería (Screenshots)", Toast.LENGTH_SHORT).show();
                    return true;
                }
            } else {
                // Para versiones anteriores a Android 10, verificamos permisos
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    if (checkSelfPermission(android.Manifest.permission.WRITE_EXTERNAL_STORAGE) != android.content.pm.PackageManager.PERMISSION_GRANTED) {
                        Log.e(TAG, "No permission to write to external storage");
                        Toast.makeText(this, "Sin permiso para guardar en la galería. Ve a Ajustes > Aplicaciones > FloatingAI > Permisos para habilitarlo", Toast.LENGTH_LONG).show();
                        
                        // Abrir la pantalla de permisos de la aplicación
                        Intent intent = new Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                        Uri uri = Uri.fromParts("package", getPackageName(), null);
                        intent.setData(uri);
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        startActivity(intent);
                        
                        return false;
                    }
                }
                
                String fileName = title + ".jpg";
                File directory = new File(android.os.Environment.getExternalStoragePublicDirectory(
                        android.os.Environment.DIRECTORY_PICTURES), "Screenshots");
                
                if (!directory.exists()) {
                    directory.mkdirs();
                }
                
                File file = new File(directory, fileName);
                try (java.io.FileOutputStream out = new java.io.FileOutputStream(file)) {
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 95, out);
                }
                
                // Notificar a la galería que se ha añadido una imagen
                Intent mediaScanIntent = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
                mediaScanIntent.setData(Uri.fromFile(file));
                sendBroadcast(mediaScanIntent);
                
                Log.d(TAG, "Image saved to gallery: " + file.getAbsolutePath());
                Toast.makeText(this, "Imagen guardada en la galería (Screenshots)", Toast.LENGTH_SHORT).show();
                return true;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error saving image to gallery", e);
            Toast.makeText(this, "Error al guardar imagen en la galería: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
        
        return false;
    }

    private void sendToAI(String message, Bitmap image) {
        // Recargar configuración para asegurar que tenemos la API key más reciente
        loadSettings();
        
        if (apiKey.isEmpty()) {
            addMessageToChat("Sistema", "Error: API key no configurada. Ve a Configuración para configurarla.", false);
            return;
        }

        if (selectedModel.isEmpty()) {
            addMessageToChat("Sistema", "Error: Ningún modelo seleccionado. Ve a Configuración para seleccionar o añadir un modelo.", false);
            return;
        }
        
        Log.d(TAG, "sendToAI called - Message: '" + message + "', Has image: " + (image != null));
        
        addMessageToChat("Sistema", "Enviando mensaje...", false);
        // Attempt to restore focus immediately after showing "Enviando mensaje..."
        if (messageInput != null) {
            messageInput.post(new Runnable() {
                @Override
                public void run() {
                    messageInput.requestFocus();
                    // InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
                    // if (imm != null) {
                        // We don't necessarily need to show the keyboard here if it was already visible,
                        // focusing should be enough. If keyboard closes, the later calls will handle it.
                        // imm.showSoftInput(messageInput, InputMethodManager.SHOW_IMPLICIT); // Or consider not calling showSoftInput here
                        // Log.d(TAG, "sendToAI: Re-focused messageInput after 'Enviando mensaje...'");
                    // } else {
                        // Log.w(TAG, "sendToAI: InputMethodManager is null when re-focusing after 'Enviando mensaje...'");
                    // }
                    Log.d(TAG, "sendToAI: Re-focused messageInput after 'Enviando mensaje...'"); // Keep log for focus
                }
            });
        }

        // Preparar historial de mensajes
        java.util.List<OpenRouterRequest.Message> messageHistory = new java.util.ArrayList<>();

        // Add system prompt first, if available
        if (aiSystemPrompt != null && !aiSystemPrompt.trim().isEmpty()) {
            messageHistory.add(new OpenRouterRequest.Message("system", aiSystemPrompt));
            Log.d(TAG, "Added system prompt to API request: " + aiSystemPrompt);
        }

        if (currentConversationId != null) {
            SQLiteDatabase db = dbHelper.getReadableDatabase();
            Cursor cursor = null;
            try {
                cursor = db.query(
                        ChatHistoryDbHelper.MessageEntry.TABLE_NAME,
                        new String[]{ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_SENDER, ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_MESSAGE_CONTENT},
                        ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_CONVERSATION_ID + " = ?",
                        new String[]{String.valueOf(currentConversationId)},
                        null, null,
                        ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_TIMESTAMP + " DESC", // Get latest messages first for context window
                        String.valueOf(aiContextWindow) // Limit to context window size
                );
                if (cursor != null) {
                    java.util.List<OpenRouterRequest.Message> tempHistory = new java.util.ArrayList<>();
                    while (cursor.moveToNext()) {
                        String sender = cursor.getString(cursor.getColumnIndexOrThrow(ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_SENDER));
                        String content = cursor.getString(cursor.getColumnIndexOrThrow(ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_MESSAGE_CONTENT));
                        String role = "Tú".equals(sender) ? "user" : "assistant";
                        if (!"Sistema".equals(sender) && !"FloatingAI".equals(sender) && !(role.equals("assistant") && content.startsWith("¡Hola!"))) {
                            // Check if this message is the text part of the current message being sent
                            // This is to avoid duplicating the current user's text message if it's already in the loaded history
                            boolean isCurrentUserMessageBase = "user".equals(role) && content.equals(message) && image == null;
                            boolean isCurrentUserMessageWithImageTag = "user".equals(role) && image != null && content.equals(message + " [con imagen capturada]") || content.equals(message + " [con imagen compartida]");

                            if (!(isCurrentUserMessageBase || isCurrentUserMessageWithImageTag)) {
                                if (content.contains("[con imagen capturada]") || content.contains("[con imagen compartida]")) {
                                    String textPart = content.replaceAll("\\[con imagen(?: capturada| compartida)\\]", "").trim();
                                    if (!textPart.isEmpty()) {
                                        tempHistory.add(new OpenRouterRequest.Message(role, textPart));
                                    }
                                } else {
                                    tempHistory.add(new OpenRouterRequest.Message(role, content));
                                }
                            }
                        }
                    }
                    // Reverse the list to have the oldest messages first, as required by the API context order
                    java.util.Collections.reverse(tempHistory);
                    messageHistory.addAll(tempHistory);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error loading message history for API call", e);
            } finally {
                if (cursor != null) {
                    cursor.close();
                }
                // Do not close db here if dbHelper manages it globally or it's used elsewhere soon.
            }
        }
        
        // Preparar mensaje actual y añadirlo al historial para la API
        OpenRouterRequest.Message currentTurnApiMessage;
        if (image != null) {
            String imageBase64 = bitmapToBase64(image);
            Log.d(TAG, "Image converted to base64, length: " + imageBase64.length());
            OpenRouterRequest.MessageContent[] contentParts = {
                OpenRouterRequest.MessageContent.createTextContent(message), // 'message' is the original user text
                OpenRouterRequest.MessageContent.createImageContent(imageBase64)
            };
            currentTurnApiMessage = new OpenRouterRequest.Message("user", contentParts);
        } else {
            currentTurnApiMessage = new OpenRouterRequest.Message("user", message);
        }
        messageHistory.add(currentTurnApiMessage); // Add the current turn to the end of the history


        OpenRouterRequest request = new OpenRouterRequest(
            selectedModel,
            messageHistory.toArray(new OpenRouterRequest.Message[0]), // Convert list to array
            aiTemperature, // Use loaded temperature
            aiMaxTokens // Use loaded max tokens
        );
        
        Log.d(TAG, "API request created - Model: " + selectedModel + ", Temp: " + aiTemperature + ", MaxTokens: " + aiMaxTokens + ", Message count: " + messageHistory.size());
        if (messageHistory.size() > 1) {
            Log.d(TAG, "Including " + (messageHistory.size() -1) + " previous messages in the context.");
        }
        
        Call<OpenRouterResponse> call = apiService.sendMessage("Bearer " + apiKey, request);
        
        call.enqueue(new Callback<OpenRouterResponse>() {
            @Override
            public void onResponse(Call<OpenRouterResponse> call, Response<OpenRouterResponse> response) {
                new Handler(Looper.getMainLooper()).post(() -> {
                    // Remover mensaje de "enviando"
                    removeLastSystemMessage();
                    
                    if (response.isSuccessful() && response.body() != null) {
                        OpenRouterResponse aiResponse = response.body();
                        if (aiResponse.choices != null && aiResponse.choices.length > 0) {
                            String content = aiResponse.choices[0].message.content;
                            addMessageToChat("IA", content, true);
                        } else {
                            addMessageToChat("Sistema", "Error: Respuesta vacía de la IA", false);
                        }
                    } else {
                        Log.e(TAG, "API Error - Code: " + response.code() + ", Message: " + response.message());
                        addMessageToChat("Sistema", "Error: " + response.code() + " - " + response.message(), false);
                    }
                    if (messageInput != null) {
                        messageInput.post(() -> messageInput.requestFocus());
                    }
                });
            }
            
            @Override
            public void onFailure(Call<OpenRouterResponse> call, Throwable t) {
                new Handler(Looper.getMainLooper()).post(() -> {
                    removeLastSystemMessage();
                    Log.e(TAG, "API Failure: " + t.getMessage(), t);
                    addMessageToChat("Sistema", "Error de conexión: " + t.getMessage(), false);
                    if (messageInput != null) {
                        messageInput.post(() -> messageInput.requestFocus());
                    }
                });
            }
        });
    }

    private String bitmapToBase64(Bitmap bitmap) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.JPEG, 80, outputStream);
        byte[] byteArray = outputStream.toByteArray();
        return Base64.encodeToString(byteArray, Base64.NO_WRAP);
    }

    private void addMessageToChat(String sender, String message) {
        addMessageToChat(sender, message, true); // Default to save to DB
    }

    private void addMessageToChat(String sender, String message, boolean saveToDb) {
        // This method now primarily handles saving and then calls the UI update method.
        addMessageToChatUI(sender, message, saveToDb);

        if (saveToDb && !(sender.equals("FloatingAI") && message.startsWith("¡Hola!"))) { 
            if (currentConversationId == null) {
                Log.d(TAG, "addMessageToChat: currentConversationId is null, attempting to start a new one before saving message: S=''' + sender + '''");
                startNewConversationInternal(); 
                if (currentConversationId == null) {
                    Log.e(TAG, "addMessageToChat: Failed to start/get a valid conversation ID even after explicit call. Message NOT saved: S=''' + sender + ''', M=''' + message + '''");
                    return;
                } else {
                    Log.d(TAG, "addMessageToChat: New conversation successfully started/retrieved with ID: " + currentConversationId + ". Proceeding to save message.");
                }
            }

            SQLiteDatabase db = dbHelper.getWritableDatabase();
            ContentValues values = new ContentValues();
            values.put(ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_SENDER, sender);
            values.put(ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_MESSAGE_CONTENT, message);
            values.put(ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_CONVERSATION_ID, currentConversationId);
            values.put(ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_TIMESTAMP, System.currentTimeMillis()); // Add timestamp for the message
            try {
                long newRowId = db.insert(ChatHistoryDbHelper.MessageEntry.TABLE_NAME, null, values);
                Log.d(TAG, "Message saved to DB with row ID: " + newRowId + " for conversation ID: " + currentConversationId);
            } catch (Exception e) {
                Log.e(TAG, "Error saving message to DB", e);
            }
        }
    }

    // New private method to format messages with simple Markdown to HTML
    private CharSequence formatMessage(String sender, String messageContent) {
        String htmlMessage = messageContent;

        // 1. Bold: **text** -> <b>text</b>
        // Regex: matches ** followed by any characters (non-greedy) followed by **
        htmlMessage = htmlMessage.replaceAll("\\*\\*(.*?)\\*\\*", "<b>$1</b>");

        // 2. Italics: *text* (being careful with list markers and bold)
        // Regex: matches * not preceded/followed by *, letter, digit, or whitespace (for simple cases like *italic*)
        // Example: *esa* situación específica
        htmlMessage = htmlMessage.replaceAll("(?<![\\*a-zA-Z0-9\\s])\\*([^\\*\\s](?:.*?[^\\*\\s])?)\\*(?![\\*a-zA-Z0-9\\s])", "<i>$1</i>");

        // 3. Italics: _text_ -> <i>text</i>
        // Regex: matches _ not preceded/followed by letter, digit, or whitespace (for simple cases like _italic_)
        htmlMessage = htmlMessage.replaceAll("(?<![a-zA-Z0-9\\s])_([^_\\s](?:.*?[^_\\s])?)_(?![a-zA-Z0-9\\s])", "<i>$1</i>");

        // 4. Unordered list items (e.g., "* item", "- item", API uses "*   item")
        // Replaces marker with a bullet and adds a line break.
        htmlMessage = htmlMessage.replaceAll("(?m)^([\\*\\-])\\s+(.*)", "&bull; $2<br/>");

        // 5. Numbered list items (e.g., "1. item", API uses "1.  item")
        // Preserves number, adds a non-breaking space for minor indent, and a line break.
        htmlMessage = htmlMessage.replaceAll("(?m)^(\\d+\\.)\\s+(.*)", "$1&nbsp;$2<br/>");

        // 6. Replace remaining newlines with <br/> for paragraph breaks.
        // This should be done after list processing that adds <br/> to avoid excessive breaks
        // if a list item is the only thing on a line.
        // The regex for lists already adds <br/>, so this handles other newlines.
        htmlMessage = htmlMessage.replace("\n", "<br/>");

        // Combine sender (bolded) and formatted message
        String fullHtml = "<b>" + sender + ":</b> " + htmlMessage;

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return Html.fromHtml(fullHtml, Html.FROM_HTML_MODE_COMPACT);
        } else {
            //noinspection deprecation
            return Html.fromHtml(fullHtml);
        }
    }

    // New method to handle UI update for messages
    private void addMessageToChatUI(String sender, String message, boolean saveToDb /* saveToDb param might be redundant here if only for UI */) {
        if (chatHistory == null) { // chatHistory is now LinearLayout
            Log.e(TAG, "addMessageToChatUI: chatHistory LinearLayout is null!");
            return;
        }

        TextView messageView = new TextView(this);
        messageView.setText(formatMessage(sender, message));
        messageView.setTextIsSelectable(true); // Allow text selection
        messageView.setFocusable(true);
        messageView.setFocusableInTouchMode(true);
        messageView.setLongClickable(true); // Explicitly enable long clickable
        messageView.setPadding(8, 4, 8, 4); // Some padding

        // Apply font size
        if ("small".equals(chatFontSize)) {
            messageView.setTextSize(android.util.TypedValue.COMPLEX_UNIT_SP, 12);
        } else if ("large".equals(chatFontSize)) {
            messageView.setTextSize(android.util.TypedValue.COMPLEX_UNIT_SP, 18);
        } else { // medium or default
            messageView.setTextSize(android.util.TypedValue.COMPLEX_UNIT_SP, 14);
        }

        messageView.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                PopupMenu popup = new PopupMenu(FloatingService.this, v);
                popup.getMenu().add("Copiar");
                popup.setOnMenuItemClickListener(new PopupMenu.OnMenuItemClickListener() {
                    public boolean onMenuItemClick(MenuItem item) {
                        if (item.getTitle().equals("Copiar")) {
                            String textToCopy = messageView.getText().toString();
                            ClipboardManager clipboard = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
                            if (clipboard != null) {
                                ClipData clip = ClipData.newPlainText("FloatingAICopiedText", textToCopy);
                                clipboard.setPrimaryClip(clip);
                                Toast.makeText(FloatingService.this, "Mensaje copiado", Toast.LENGTH_SHORT).show();
                            } else {
                                Log.e(TAG, "ClipboardManager is null, cannot copy.");
                                Toast.makeText(FloatingService.this, "Error al copiar", Toast.LENGTH_SHORT).show();
                            }
                            return true;
                        }
                        return false;
                    }
                });
                popup.show();
                return true; // Consumes the long click event
            }
        });

        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        params.bottomMargin = 8; // Margin between messages

        if ("Tú".equals(sender)) {
            params.gravity = Gravity.END;
            messageView.setBackgroundResource(R.drawable.user_message_background); // Placeholder, create this drawable
        } else {
            params.gravity = Gravity.START;
            messageView.setBackgroundResource(R.drawable.ai_message_background); // Placeholder, create this drawable
        }
        messageView.setLayoutParams(params);

        chatHistory.addView(messageView);
        
        chatScrollView.post(() -> chatScrollView.fullScroll(View.FOCUS_DOWN));

        // The saveToDb logic is moved to the wrapper addMessageToChat method
        // This method (addMessageToChatUI) should strictly be for UI updates.
    }

    private void removeLastSystemMessage() {
        if (chatHistory != null && chatHistory.getChildCount() > 0) {
            View lastChild = chatHistory.getChildAt(chatHistory.getChildCount() - 1);
            if (lastChild instanceof TextView) {
                TextView lastMessageView = (TextView) lastChild;
                if (lastMessageView.getText().toString().startsWith("Sistema: Enviando mensaje...")) {
                    chatHistory.removeViewAt(chatHistory.getChildCount() - 1);
                }
            }
        }
    }

    private void openAccessibilitySettings() {
        Intent intent = new Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivity(intent);
    }

    private void showFavoriteModelsPopupMenu(View anchorView) {
        ModelManager modelManager = new ModelManager(this);
        List<ModelManager.AIModel> favoriteModels = modelManager.getFavoriteModels();

        if (favoriteModels.isEmpty()) {
            Toast.makeText(this, "No hay modelos favoritos configurados.", Toast.LENGTH_SHORT).show();
            return;
        }

        PopupMenu popupMenu = new PopupMenu(this, anchorView); // 'this' is the Service context
        for (ModelManager.AIModel model : favoriteModels) {
            popupMenu.getMenu().add(model.displayName); 
        }

        popupMenu.setOnMenuItemClickListener(item -> {
            String selectedDisplayName = item.getTitle().toString();
            ModelManager.AIModel chosenModel = null;
            for (ModelManager.AIModel model : favoriteModels) {
                if (model.displayName.equals(selectedDisplayName)) {
                    chosenModel = model;
                    break;
                }
            }

            if (chosenModel != null) {
                selectedModel = chosenModel.id;
                SharedPreferences.Editor editor = getSharedPreferences("app_prefs", MODE_PRIVATE).edit();
                editor.putString("selected_model", selectedModel);
                editor.apply();
                Toast.makeText(this, "Modelo seleccionado: " + chosenModel.displayName, Toast.LENGTH_SHORT).show();
                addMessageToChat("Sistema", "Nuevo modelo seleccionado: " + chosenModel.displayName, false);
            } else {
                Toast.makeText(this, "Error al seleccionar el modelo.", Toast.LENGTH_SHORT).show();
            }
            return true;
        });

        popupMenu.show();
    }

    private void confirmStartNewConversation() {
        // This method is no longer called directly for UI, 
        // but can be kept if other logic might need it with a proper context later.
        // For now, the new chat button directly calls startNewConversationInternal and updates UI.
        new AlertDialog.Builder(this) 
                .setTitle("Nueva Conversación")
                .setMessage("¿Estás seguro de que quieres iniciar una nueva conversación? La conversación actual se guardará y se iniciará una nueva.")
                .setPositiveButton("Iniciar Nueva", new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int which) {
                        startNewConversationInternal();
                        // Clear the UI and add welcome message
                        if (chatHistory != null) {
                            chatHistory.removeAllViews();
                        }
                        addMessageToChat("FloatingAI", "¡Hola! Nueva conversación iniciada.", false);
                        Toast.makeText(FloatingService.this, "Nueva conversación iniciada", Toast.LENGTH_SHORT).show();
                    }
                })
                .setNegativeButton("Cancelar", null)
                .setIcon(android.R.drawable.ic_dialog_alert)
                .show(); // This line causes the crash if 'this' is not an Activity context
    }

    private void startNewConversationInternal() {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        ContentValues values = new ContentValues();
        // For now, just creating an entry is enough to get an ID.
        values.put(ChatHistoryDbHelper.ConversationEntry.COLUMN_NAME_START_TIMESTAMP, System.currentTimeMillis()); // Ensure timestamp is added
        try {
            long newConversationRowId = db.insert(ChatHistoryDbHelper.ConversationEntry.TABLE_NAME, null, values); // Use null for nullColumnHack when values is not empty
            if (newConversationRowId != -1) {
                currentConversationId = newConversationRowId;
                Log.d(TAG, "Started new conversation with ID: " + currentConversationId);
            } else {
                Log.e(TAG, "Failed to insert new conversation into database. Insert returned -1.");
                currentConversationId = null; // Ensure it's null if failed
            }
        } catch (Exception e) {
            Log.e(TAG, "Error starting new conversation", e);
            currentConversationId = null; // Ensure it's null on error
        }
        // db.close(); // Don't close here
    }

    private void clearCurrentChatAndHistory() {
        // This method is now effectively starting a new conversation.
        // The actual deletion of old conversation data will be handled by a separate mechanism
        // or when a conversation is explicitly deleted from the history view.

        startNewConversationInternal();

        if (chatHistory != null) {
            chatHistory.removeAllViews();
        }
        // Add a welcome message for the new conversation
        addMessageToChat("FloatingAI", "¡Hola! Nueva conversación iniciada.", false); 
        Toast.makeText(this, "Nueva conversación iniciada", Toast.LENGTH_SHORT).show();

        // The old deletion logic is removed as we are not deleting all messages anymore,
        // just starting a new conversation context.
        // SQLiteDatabase db = dbHelper.getWritableDatabase();
        // try {
        //     db.delete(ChatHistoryDbHelper.MessageEntry.TABLE_NAME, null, null);
        //     Log.d(TAG, "Chat history cleared from DB for new conversation.");
        //     addMessageToChat("FloatingAI", "¡Hola! Nueva conversación iniciada.", false);
        //     Toast.makeText(this, "Nueva conversación iniciada", Toast.LENGTH_SHORT).show();
        // } catch (Exception e) {
        //     Log.e(TAG, "Error clearing chat history for new conversation", e);
        //     Toast.makeText(this, "Error al iniciar nueva conversación", Toast.LENGTH_SHORT).show();
        // }
    }

    private void showPromptButtons(final Bitmap imageForPrompt) {
        if (promptButtonsContainer == null || imageForPrompt == null) {
            Log.d(TAG, "showPromptButtons: Container is null or no image, not showing buttons.");
            if (promptButtonsContainer != null) {
                 promptButtonsContainer.setVisibility(View.GONE);
                 promptButtonsContainer.removeAllViews();
            }
            return;
        }

        promptButtonsContainer.removeAllViews(); // Clear any existing buttons
        boolean hasVisibleButton = false;
        
        // Create a list to hold all enabled buttons
        java.util.List<Button> enabledButtons = new java.util.ArrayList<>();

        if (promptButton1Enabled && promptButton1Text != null && !promptButton1Text.isEmpty()) {
            Button button1 = new Button(this);
            button1.setText(promptButton1Text);
            button1.setOnClickListener(v -> {
                addMessageToChat("Tú", promptButton1Text + " [con imagen]");
                sendToAI(promptButton1Text, imageForPrompt);
                clearPromptButtonsAndSharedImage();
            });
            enabledButtons.add(button1);
            hasVisibleButton = true;
        }

        if (promptButton2Enabled && promptButton2Text != null && !promptButton2Text.isEmpty()) {
            Button button2 = new Button(this);
            button2.setText(promptButton2Text);
            button2.setOnClickListener(v -> {
                addMessageToChat("Tú", promptButton2Text + " [con imagen]");
                sendToAI(promptButton2Text, imageForPrompt);
                clearPromptButtonsAndSharedImage();
            });
            enabledButtons.add(button2);
            hasVisibleButton = true;
        }

        if (promptButton3Enabled && promptButton3Text != null && !promptButton3Text.isEmpty()) {
            Button button3 = new Button(this);
            button3.setText(promptButton3Text);
            button3.setOnClickListener(v -> {
                addMessageToChat("Tú", promptButton3Text + " [con imagen]");
                sendToAI(promptButton3Text, imageForPrompt);
                clearPromptButtonsAndSharedImage();
            });
            enabledButtons.add(button3);
            hasVisibleButton = true;
        }

        if (promptButton4Enabled && promptButton4Text != null && !promptButton4Text.isEmpty()) {
            Button button4 = new Button(this);
            button4.setText(promptButton4Text);
            button4.setOnClickListener(v -> {
                addMessageToChat("Tú", promptButton4Text + " [con imagen]");
                sendToAI(promptButton4Text, imageForPrompt);
                clearPromptButtonsAndSharedImage();
            });
            enabledButtons.add(button4);
            hasVisibleButton = true;
        }
        
        // Organize buttons in rows of maximum 2 buttons each
        if (hasVisibleButton) {
            for (int i = 0; i < enabledButtons.size(); i += 2) {
                LinearLayout rowLayout = new LinearLayout(this);
                rowLayout.setOrientation(LinearLayout.HORIZONTAL);
                rowLayout.setGravity(android.view.Gravity.CENTER);
                LinearLayout.LayoutParams rowParams = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT
                );
                rowParams.setMargins(0, 0, 0, dpToPx(4)); // 4dp bottom margin for each row
                rowLayout.setLayoutParams(rowParams);
                
                // Add first button in this row
                Button firstButton = enabledButtons.get(i);
                LinearLayout.LayoutParams buttonParams = new LinearLayout.LayoutParams(
                    0,
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    1.0f // Equal weight
                );
                buttonParams.setMarginEnd(dpToPx(4)); // 4dp right margin
                firstButton.setLayoutParams(buttonParams);
                firstButton.setTextSize(android.util.TypedValue.COMPLEX_UNIT_SP, 12); // Smaller text
                firstButton.setMaxLines(2); // Allow text to wrap to 2 lines
                firstButton.setEllipsize(android.text.TextUtils.TruncateAt.END);
                rowLayout.addView(firstButton);
                
                // Add second button if it exists
                if (i + 1 < enabledButtons.size()) {
                    Button secondButton = enabledButtons.get(i + 1);
                    LinearLayout.LayoutParams secondButtonParams = new LinearLayout.LayoutParams(
                        0,
                        LinearLayout.LayoutParams.WRAP_CONTENT,
                        1.0f // Equal weight
                    );
                    secondButtonParams.setMarginStart(dpToPx(4)); // 4dp left margin
                    secondButton.setLayoutParams(secondButtonParams);
                    secondButton.setTextSize(android.util.TypedValue.COMPLEX_UNIT_SP, 12); // Smaller text
                    secondButton.setMaxLines(2); // Allow text to wrap to 2 lines
                    secondButton.setEllipsize(android.text.TextUtils.TruncateAt.END);
                    rowLayout.addView(secondButton);
                }
                
                promptButtonsContainer.addView(rowLayout);
            }
            
            promptButtonsContainer.setVisibility(View.VISIBLE);
            Log.d(TAG, "Prompt buttons shown in organized rows.");
        } else {
            promptButtonsContainer.setVisibility(View.GONE);
            Log.d(TAG, "No prompt buttons are enabled or configured.");
        }
    }

    private void clearPromptButtonsAndSharedImage() {
        if (promptButtonsContainer != null) {
            promptButtonsContainer.removeAllViews();
            promptButtonsContainer.setVisibility(View.GONE);
        }
        if (currentSharedImage != null && !currentSharedImage.isRecycled()) {
            currentSharedImage.recycle();
            currentSharedImage = null;
        }
        if (pendingCapturedImageBitmap != null && !pendingCapturedImageBitmap.isRecycled()) {
            pendingCapturedImageBitmap.recycle();
            pendingCapturedImageBitmap = null;
        }
        currentActiveImageForPromptButtons = null; // Clear the active image reference
        messageInput.setText(""); // Clear text input as well
    }

    // Helper method to convert dp to pixels
    private int dpToPx(int dp) {
        return (int) (dp * getResources().getDisplayMetrics().density);
    }

    @Override
    public void onDestroy() {
        // Desregistrar el receptor de cambios de orientación
        try {
            unregisterReceiver(orientationChangeReceiver);
            Log.d(TAG, "Orientation change receiver unregistered.");
        } catch (Exception e) {
            Log.e(TAG, "Error unregistering orientation change receiver", e);
        }
        
        super.onDestroy();
        
        // Save final window state before destroying
        if (isChatVisible && chatParams != null) {
            saveChatWindowState();
        }
        
        // Clean up handlers
        if (saveStateRunnable != null) {
            saveStateHandler.removeCallbacks(saveStateRunnable);
        }
        
        if (dbHelper != null) { // Add null check for dbHelper before closing
            dbHelper.close(); 
        }
        Log.d(TAG, "FloatingService onDestroy() called, DB closed, and views removed"); 
        if (floatingBubble != null && floatingBubble.isAttachedToWindow()) { // This should be before the log if it removes views
            windowManager.removeView(floatingBubble);
            floatingBubble = null;
        }
        if (isChatUiInitialized && chatWindow != null && chatWindow.isAttachedToWindow()) {
            windowManager.removeView(chatWindow);
            chatWindow = null;
            isChatUiInitialized = false; 
        }
        if (dismissView != null && dismissView.isAttachedToWindow() && isDismissViewVisible) { 
            windowManager.removeView(dismissView);
            dismissView = null;
            isDismissViewVisible = false;
        }
        LocalBroadcastManager.getInstance(this).unregisterReceiver(captureCancelReceiver);
    }

    // Método para ajustar la ventana de chat a la orientación actual
    private void adjustChatWindowToOrientation() {
        if (chatWindow == null || !isChatVisible || chatParams == null) {
            Log.d(TAG, "adjustChatWindowToOrientation: No se puede ajustar, chatWindow=" + 
                  (chatWindow != null) + ", isChatVisible=" + isChatVisible + ", chatParams=" + (chatParams != null));
            return;
        }
        
        // Obtener dimensiones actuales de la pantalla
        DisplayMetrics displayMetrics = new DisplayMetrics();
        windowManager.getDefaultDisplay().getMetrics(displayMetrics);
        int screenWidth = displayMetrics.widthPixels;
        int screenHeight = displayMetrics.heightPixels;
        
        // Determinar orientación
        boolean isLandscape = screenWidth > screenHeight;
        
        Log.d(TAG, "Ajustando ventana de chat. Orientación: " + 
              (isLandscape ? "horizontal" : "vertical") + 
              ", Pantalla: " + screenWidth + "x" + screenHeight);
        
        // Actualizar límites según dimensiones de pantalla
        maxChatWidthPx = screenWidth;
        maxChatHeightPx = screenHeight;
        
        // Calcular nuevas dimensiones (proporción de la pantalla)
        int newWidth = isLandscape ? (int)(screenWidth * 0.7f) : (int)(screenWidth * 0.8f);
        int newHeight = isLandscape ? (int)(screenHeight * 0.8f) : (int)(screenHeight * 0.6f);
        
        // Asegurar que la ventana esté dentro de los límites de la pantalla
        chatParams.width = Math.max(minChatWidthPx, Math.min(newWidth, maxChatWidthPx));
        chatParams.height = Math.max(minChatHeightPx, Math.min(newHeight, maxChatHeightPx));
        
        // Asegurar que la posición sigue siendo válida
        chatParams.x = Math.max(0, Math.min(chatParams.x, screenWidth - chatParams.width));
        chatParams.y = Math.max(0, Math.min(chatParams.y, screenHeight - chatParams.height));
        
        Log.d(TAG, "Nueva dimensión de ventana: " + chatParams.width + "x" + chatParams.height +
              " en posición (" + chatParams.x + "," + chatParams.y + ")");
        
        // Actualizar la ventana
        try {
            if (chatWindow != null && chatWindow.isAttachedToWindow()) {
                windowManager.updateViewLayout(chatWindow, chatParams);
                // Guardar el nuevo estado
                scheduleSaveChatWindowState();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error al actualizar la ventana de chat durante el cambio de orientación", e);
        }
    }

    // Adding default values for buttons 3 and 4
    public static final String DEFAULT_PROMPT_BUTTON_3_TEXT = "Analiza imagen";
    public static final boolean DEFAULT_PROMPT_BUTTON_3_ENABLED = true; // Cambio a true
    public static final String DEFAULT_PROMPT_BUTTON_4_TEXT = "Traduce texto";
    public static final boolean DEFAULT_PROMPT_BUTTON_4_ENABLED = false;
}