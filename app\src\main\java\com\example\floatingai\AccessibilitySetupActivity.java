package com.example.floatingai;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;
import android.widget.Button;
import android.widget.TextView;

public class AccessibilitySetupActivity extends Activity {
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Crear layout programáticamente
        android.widget.LinearLayout layout = new android.widget.LinearLayout(this);
        layout.setOrientation(android.widget.LinearLayout.VERTICAL);
        layout.setPadding(40, 40, 40, 40);
        
        // Título
        TextView title = new TextView(this);
        title.setText("Configurar Servicio de Accesibilidad");
        title.setTextSize(20);
        title.setTextColor(getResources().getColor(android.R.color.black));
        title.setPadding(0, 0, 0, 20);
        layout.addView(title);
        
        // Instrucciones
        TextView instructions = new TextView(this);
        instructions.setText("Para capturar pantalla sin permisos repetidos:\n\n" +
                           "1. Toca 'Abrir Configuración'\n" +
                           "2. Busca 'FloatingAI' en la lista\n" +
                           "3. Activa el interruptor\n" +
                           "4. Confirma en el diálogo\n" +
                           "5. Regresa a la aplicación\n\n" +
                           "Esto permitirá capturas instantáneas.");
        instructions.setTextSize(16);
        instructions.setTextColor(getResources().getColor(android.R.color.darker_gray));
        instructions.setPadding(0, 0, 0, 30);
        layout.addView(instructions);
        
        // Botón para abrir configuración
        Button openSettingsButton = new Button(this);
        openSettingsButton.setText("Abrir Configuración de Accesibilidad");
        openSettingsButton.setOnClickListener(v -> {
            Intent intent = new Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS);
            startActivity(intent);
        });
        layout.addView(openSettingsButton);
        
        // Botón para cerrar
        Button closeButton = new Button(this);
        closeButton.setText("Cerrar");
        closeButton.setOnClickListener(v -> finish());
        closeButton.setPadding(0, 20, 0, 0);
        layout.addView(closeButton);
        
        setContentView(layout);
    }
} 