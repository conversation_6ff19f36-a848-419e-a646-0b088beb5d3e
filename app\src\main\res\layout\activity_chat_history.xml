<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    android:fitsSystemWindows="true"
    tools:context=".ChatHistoryActivity">

    <ImageButton
        android:id="@+id/delete_all_conversations_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@android:drawable/ic_menu_delete"
        android:contentDescription="Eliminar todas las conversaciones"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="8dp"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/conversations_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/delete_all_conversations_button"
        android:layout_marginTop="8dp"
        android:scrollbars="vertical" />

    <TextView
        android:id="@+id/empty_history_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="No hay conversaciones guardadas."
        android:gravity="center"
        android:textSize="16sp"
        android:visibility="gone" 
        android:layout_marginTop="16dp"/>

</RelativeLayout> 