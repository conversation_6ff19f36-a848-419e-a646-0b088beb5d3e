package com.example.floatingai;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.Headers;
import retrofit2.http.POST;

public interface OpenRouterAPI {
    
    @Headers({
        "Content-Type: application/json",
        "HTTP-Referer: https://floatingai.app",
        "X-Title: FloatingAI"
    })
    @POST("chat/completions")
    Call<OpenRouterResponse> sendMessage(
        @Header("Authorization") String authorization,
        @Body OpenRouterRequest request
    );
}

class OpenRouterRequest {
    public String model;
    public Message[] messages;
    public double temperature;
    public int max_tokens;

    public OpenRouterRequest(String model, Message[] messages, double temperature, int max_tokens) {
        this.model = model;
        this.messages = messages;
        this.temperature = temperature;
        this.max_tokens = max_tokens;
    }

    public static class Message {
        public String role;
        public Object content;

        // Constructor para mensajes de texto
        public Message(String role, String text) {
            this.role = role;
            this.content = text;
        }

        // Constructor para mensajes con imagen
        public Message(String role, MessageContent[] content) {
            this.role = role;
            this.content = content;
        }
    }

    public static class MessageContent {
        public String type;
        public String text;
        public ImageUrl image_url;

        // Constructor privado
        private MessageContent() {}

        // Método factory para contenido de texto
        public static MessageContent createTextContent(String text) {
            MessageContent content = new MessageContent();
            content.type = "text";
            content.text = text;
            return content;
        }

        // Método factory para contenido de imagen
        public static MessageContent createImageContent(String imageBase64) {
            MessageContent content = new MessageContent();
            content.type = "image_url";
            content.image_url = new ImageUrl("data:image/jpeg;base64," + imageBase64);
            return content;
        }

        public static class ImageUrl {
            public String url;

            public ImageUrl(String url) {
                this.url = url;
            }
        }
    }
}

class OpenRouterResponse {
    public String id;
    public String object;
    public long created;
    public String model;
    public Choice[] choices;
    public Usage usage;

    public static class Choice {
        public int index;
        public Message message;
        public String finish_reason;

        public static class Message {
            public String role;
            public String content;
        }
    }

    public static class Usage {
        public int prompt_tokens;
        public int completion_tokens;
        public int total_tokens;
    }
} 