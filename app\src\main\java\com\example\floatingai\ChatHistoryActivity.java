package com.example.floatingai;

import android.content.DialogInterface;
import android.content.Intent;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class ChatHistoryActivity extends AppCompatActivity implements ConversationAdapter.OnConversationClickListener {

    private static final String TAG = "ChatHistoryActivity";
    private RecyclerView conversationsRecyclerView;
    private ConversationAdapter conversationAdapter;
    private List<ConversationAdapter.ConversationItem> conversationList;
    private ChatHistoryDbHelper dbHelper;
    private TextView emptyHistoryTextView;
    private ImageButton deleteAllButton;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_chat_history);

        dbHelper = new ChatHistoryDbHelper(this);

        conversationsRecyclerView = findViewById(R.id.conversations_recycler_view);
        emptyHistoryTextView = findViewById(R.id.empty_history_text);
        conversationsRecyclerView.setLayoutManager(new LinearLayoutManager(this));

        deleteAllButton = findViewById(R.id.delete_all_conversations_button);
        deleteAllButton.setOnClickListener(v -> showDeleteAllConfirmationDialog());

        conversationList = new ArrayList<>();
        conversationAdapter = new ConversationAdapter(this, conversationList, this);

        conversationsRecyclerView.setAdapter(conversationAdapter);

        loadConversations();
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadConversations();
    }

    private void loadConversations() {
        Log.d(TAG, "Loading conversations...");
        conversationList.clear();
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        Cursor cursor = null;

        try {
            cursor = db.query(
                    ChatHistoryDbHelper.ConversationEntry.TABLE_NAME,
                    new String[]{ChatHistoryDbHelper.ConversationEntry._ID, ChatHistoryDbHelper.ConversationEntry.COLUMN_NAME_START_TIMESTAMP},
                    null, null, null, null,
                    ChatHistoryDbHelper.ConversationEntry.COLUMN_NAME_START_TIMESTAMP + " DESC"
            );

            if (cursor != null) {
                Log.d(TAG, "Cursor count: " + cursor.getCount());
                while (cursor.moveToNext()) {
                    long id = cursor.getLong(cursor.getColumnIndexOrThrow(ChatHistoryDbHelper.ConversationEntry._ID));
                    long timestamp = cursor.getLong(cursor.getColumnIndexOrThrow(ChatHistoryDbHelper.ConversationEntry.COLUMN_NAME_START_TIMESTAMP));
                    Log.d(TAG, "Found conversation in ChatHistoryActivity - ID: " + id + ", Timestamp: " + timestamp);
                    conversationList.add(new ConversationAdapter.ConversationItem(id, timestamp));
                }
            } else {
                Log.w(TAG, "Cursor was null in loadConversations");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error loading conversations from DB", e);
            Toast.makeText(this, "Error al cargar historial", Toast.LENGTH_SHORT).show();
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            // db.close(); // DbHelper handles this
        }

        conversationAdapter.updateData(conversationList);

        if (conversationList.isEmpty()) {
            emptyHistoryTextView.setVisibility(View.VISIBLE);
            conversationsRecyclerView.setVisibility(View.GONE);
            deleteAllButton.setVisibility(View.GONE);
        } else {
            emptyHistoryTextView.setVisibility(View.GONE);
            conversationsRecyclerView.setVisibility(View.VISIBLE);
            deleteAllButton.setVisibility(View.VISIBLE);
        }
        Log.d(TAG, "Conversations loaded: " + conversationList.size());
    }

    @Override
    public void onConversationClick(long conversationId) {
        Log.d(TAG, "Conversation clicked, ID: " + conversationId);
        Intent intent = new Intent(this, ConversationViewActivity.class);
        intent.putExtra("conversation_id", conversationId);
        startActivity(intent);
    }

    private void showDeleteAllConfirmationDialog() {
        new AlertDialog.Builder(this)
                .setTitle("Eliminar Historial")
                .setMessage("¿Estás seguro de que quieres eliminar TODAS las conversaciones? Esta acción no se puede deshacer.")
                .setPositiveButton("Eliminar Todo", (dialog, which) -> deleteAllConversations())
                .setNegativeButton("Cancelar", null)
                .setIcon(android.R.drawable.ic_dialog_alert)
                .show();
    }

    private void deleteAllConversations() {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        try {
            db.delete(ChatHistoryDbHelper.MessageEntry.TABLE_NAME, null, null);
            db.delete(ChatHistoryDbHelper.ConversationEntry.TABLE_NAME, null, null);
            Toast.makeText(this, "Todas las conversaciones han sido eliminadas.", Toast.LENGTH_SHORT).show();
            loadConversations(); // Refresh the list
        } catch (Exception e) {
            Log.e("ChatHistoryActivity", "Error deleting all conversations", e);
            Toast.makeText(this, "Error al eliminar las conversaciones.", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    protected void onDestroy() {
        dbHelper.close();
        super.onDestroy();
    }
} 