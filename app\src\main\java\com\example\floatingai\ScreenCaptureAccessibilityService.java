package com.example.floatingai;

import android.accessibilityservice.AccessibilityService;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.ColorSpace;
import android.hardware.HardwareBuffer;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.Display;
import android.view.accessibility.AccessibilityEvent;
import android.widget.Toast;
import androidx.annotation.RequiresApi;

import java.io.File;
import java.io.FileOutputStream;

public class ScreenCaptureAccessibilityService extends AccessibilityService {
    
    private static final String TAG = "ScreenCaptureAccess";
    private static ScreenCaptureAccessibilityService instance;
    
    @Override
    public void onCreate() {
        super.onCreate();
        instance = this;
        Log.d(TAG, "Service created");
    }
    
    @Override
    public void onAccessibilityEvent(AccessibilityEvent event) {
        // No necesitamos procesar eventos aquí
    }
    
    @Override
    public void onInterrupt() {
        Log.d(TAG, "Service interrupted");
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        instance = null;
        Log.d(TAG, "Service destroyed");
    }
    
    public static boolean isServiceEnabled() {
        return instance != null;
    }
    
    public static ScreenCaptureAccessibilityService getInstance() {
        return instance;
    }
    
    @RequiresApi(api = Build.VERSION_CODES.R)
    public void captureScreen(String userMessage) {
        Log.d(TAG, "Attempting to capture screen via Accessibility Service");
        
        // TAKE_SCREENSHOT_FULLSCREEN is 1, TAKE_SCREENSHOT_PROVIDED_DISPLAY is 2
        // We'll use 1 for full screen capture.
        takeScreenshot(Display.DEFAULT_DISPLAY, getMainExecutor(), new TakeScreenshotCallback() {
            @Override
            public void onSuccess(ScreenshotResult screenshotResult) {
                Log.d(TAG, "Screenshot success");
                try {
                    HardwareBuffer buffer = screenshotResult.getHardwareBuffer();
                    ColorSpace colorSpace = screenshotResult.getColorSpace();
                    
                    if (buffer == null) {
                        Log.e(TAG, "HardwareBuffer is null");
                        showError("Error: No se pudo obtener el buffer de la imagen.");
                        buffer.close(); // Asegurarse de cerrar incluso si es nulo después de la verificación
                        return;
                    }
                    if (colorSpace == null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        // Older versions might not provide ColorSpace, try a default
                        colorSpace = ColorSpace.get(ColorSpace.Named.SRGB);
                         Log.w(TAG, "ColorSpace was null, using SRGB as default.");
                    }

                    Bitmap bitmap = Bitmap.wrapHardwareBuffer(buffer, colorSpace);
                    buffer.close(); // Important to close the buffer

                    if (bitmap != null) {
                        Log.d(TAG, "Bitmap created from buffer");
                        String screenshotPath = saveBitmapAndGetPath(bitmap);
                        if (screenshotPath != null) {
                            // Introducir un pequeño retraso ANTES de lanzar AreaSelectionActivity
                            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                                launchAreaSelection(screenshotPath, userMessage);
                            }, 100); // Reducido a 100ms ya que FloatingService ahora tiene un retraso de 500ms
                        } else {
                            showError("Error al guardar la captura.");
                        }
                        bitmap.recycle(); // Recycle bitmap after use
                    } else {
                        Log.e(TAG, "Failed to create bitmap from buffer.");
                        showError("Error: No se pudo crear el bitmap de la captura.");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error processing screenshot buffer", e);
                    showError("Error al procesar captura: " + e.getMessage());
                }
            }

            @Override
            public void onFailure(int errorCode) {
                Log.e(TAG, "Screenshot failed, error code: " + errorCode);
                showError("Error al capturar pantalla (Código: " + errorCode + ")");
            }
        });
    }
    
    private String saveBitmapAndGetPath(Bitmap bitmap) {
        try {
            String filename = "accessibility_shot_" + System.currentTimeMillis() + ".jpg";
            File file = new File(getFilesDir(), filename);
            try (FileOutputStream fos = new FileOutputStream(file)) {
                bitmap.compress(Bitmap.CompressFormat.JPEG, 90, fos);
            }
            Log.d(TAG, "Screenshot saved to: " + file.getAbsolutePath());
            return file.getAbsolutePath();
        } catch (Exception e) {
            Log.e(TAG, "Error saving bitmap", e);
            return null;
        }
    }
    
    private void launchAreaSelection(String screenshotPath, String userMessage) {
        Intent intent = new Intent(this, AreaSelectionActivity.class);
        intent.putExtra("full_screen_path", screenshotPath);
        intent.putExtra("user_message", userMessage);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivity(intent);
        Log.d(TAG, "Launched AreaSelectionActivity");
    }
    
    private void showError(String message) {
        getMainExecutor().execute(() -> Toast.makeText(this, message, Toast.LENGTH_LONG).show());
    }
} 