# Solución para problema de instalación en Xiaomi 12 Pro

## Cambios realizados para mejorar la compatibilidad:

### 1. Reducción del minSdk
- **Antes**: minSdk 30 (Android 11+)
- **Ahora**: minSdk 24 (Android 7.0+)
- **<PERSON><PERSON><PERSON>**: Mayor compatibilidad con dispositivos más antiguos

### 2. Ajuste del targetSdk
- **Antes**: targetSdk 35
- **Ahora**: targetSdk 34
- **<PERSON><PERSON><PERSON>**: Mayor estabilidad y menos restricciones

### 3. Configuración de arquitecturas ARM
- Agregado soporte explícito para `arm64-v8a` y `armeabi-v7a`
- Asegura compatibilidad con el procesador del Xiaomi 12 Pro

### 4. Mejoras en permisos de almacenamiento
- Configuración condicional de permisos según la versión de Android
- Soporte para `requestLegacyExternalStorage` y `preserveLegacyExternalStorage`

### 5. Configuración de red mejorada
- Agregado `network_security_config.xml` para mejor manejo de conexiones HTTP/HTTPS

## Pasos para generar el nuevo APK:

### Opción 1: Usando Android Studio
1. Abre el proyecto en Android Studio
2. Ve a **Build** → **Clean Project**
3. Luego **Build** → **Build Bundle(s) / APK(s)** → **Build APK(s)**
4. El APK se generará en `app/build/outputs/apk/debug/`

### Opción 2: Usando línea de comandos (si tienes Java configurado)
```bash
./gradlew clean
./gradlew assembleDebug
```

## Configuraciones adicionales para MIUI (Xiaomi):

### En el dispositivo Xiaomi:
1. **Habilitar instalación de fuentes desconocidas**:
   - Configuración → Privacidad → Fuentes desconocidas
   - Activar para el navegador o gestor de archivos que uses

2. **Desactivar MIUI Optimization** (temporal):
   - Configuración → Configuración adicional → Para desarrolladores
   - Desactivar "MIUI optimization"
   - Reiniciar el dispositivo

3. **Verificar espacio de almacenamiento**:
   - Asegurar que hay al menos 100MB libres

4. **Limpiar caché del instalador**:
   - Configuración → Aplicaciones → Instalador de paquetes
   - Limpiar caché y datos

### Si sigue fallando:
1. **Instalar usando ADB**:
   ```bash
   adb install -r app-debug.apk
   ```

2. **Verificar logs de error**:
   ```bash
   adb logcat | grep -i "install"
   ```

## Problemas comunes en MIUI:

1. **Firma de aplicación**: MIUI es estricto con las firmas
2. **Permisos especiales**: `SYSTEM_ALERT_WINDOW` requiere autorización manual
3. **Optimizaciones de batería**: Pueden interferir con servicios en segundo plano

## Después de la instalación:

1. **Otorgar permisos manualmente**:
   - Configuración → Aplicaciones → FloatingAI → Permisos
   - Activar todos los permisos necesarios

2. **Desactivar optimización de batería**:
   - Configuración → Batería → Optimización de batería
   - Buscar FloatingAI y seleccionar "No optimizar"

3. **Permitir ventanas emergentes**:
   - Configuración → Aplicaciones → FloatingAI → Otras configuraciones
   - Activar "Mostrar ventanas emergentes"
