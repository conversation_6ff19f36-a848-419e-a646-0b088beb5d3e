package com.example.floatingai;

import android.content.DialogInterface;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.List;

public class ConversationViewActivity extends AppCompatActivity {

    private static final String TAG = "ConversationViewActivity";
    private RecyclerView messagesRecyclerView;
    private MessageAdapter messageAdapter;
    private List<MessageAdapter.ChatMessage> messageList;
    private ChatHistoryDbHelper dbHelper;
    private TextView emptyMessagesText;
    private Button deleteConversationButton;
    private long conversationId = -1;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_conversation_view);

        conversationId = getIntent().getLongExtra("conversation_id", -1);
        if (conversationId == -1) {
            Toast.makeText(this, "Error: ID de conversación no válido.", Toast.LENGTH_LONG).show();
            Log.e(TAG, "Invalid conversation_id passed.");
            finish();
            return;
        }

        dbHelper = new ChatHistoryDbHelper(this);

        messagesRecyclerView = findViewById(R.id.messages_recycler_view);
        emptyMessagesText = findViewById(R.id.empty_messages_text);
        deleteConversationButton = findViewById(R.id.delete_conversation_button);

        messageList = new ArrayList<>();
        messageAdapter = new MessageAdapter(this, messageList);
        messagesRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        messagesRecyclerView.setAdapter(messageAdapter);

        deleteConversationButton.setOnClickListener(v -> confirmDeleteConversation());

        loadMessages();
    }

    private void loadMessages() {
        Log.d(TAG, "Loading messages for conversation ID: " + conversationId);
        messageList.clear();
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        Cursor cursor = null;

        String[] projection = {
                ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_SENDER,
                ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_MESSAGE_CONTENT
        };
        String selection = ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_CONVERSATION_ID + " = ?";
        String[] selectionArgs = { String.valueOf(conversationId) };
        String sortOrder = ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_TIMESTAMP + " ASC";

        try {
            cursor = db.query(
                    ChatHistoryDbHelper.MessageEntry.TABLE_NAME,
                    projection,
                    selection, selectionArgs, null, null,
                    sortOrder
            );

            if (cursor != null) {
                Log.d(TAG, "Message cursor count for conversation ID " + conversationId + ": " + cursor.getCount());
                while (cursor.moveToNext()) {
                    String sender = cursor.getString(cursor.getColumnIndexOrThrow(ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_SENDER));
                    String content = cursor.getString(cursor.getColumnIndexOrThrow(ChatHistoryDbHelper.MessageEntry.COLUMN_NAME_MESSAGE_CONTENT));
                    Log.d(TAG, "Found message: S=" + sender + ", C=" + content);
                    messageList.add(new MessageAdapter.ChatMessage(sender, content));
                }
            } else {
                Log.w(TAG, "Message cursor was null for conversation ID " + conversationId);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error loading messages from DB", e);
            Toast.makeText(this, "Error al cargar mensajes", Toast.LENGTH_SHORT).show();
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        messageAdapter.updateData(messageList);

        if (messageList.isEmpty()) {
            emptyMessagesText.setVisibility(View.VISIBLE);
            messagesRecyclerView.setVisibility(View.GONE);
        } else {
            emptyMessagesText.setVisibility(View.GONE);
            messagesRecyclerView.setVisibility(View.VISIBLE);
        }
        Log.d(TAG, "Messages loaded: " + messageList.size());
    }

    private void confirmDeleteConversation() {
        new AlertDialog.Builder(this)
                .setTitle("Eliminar Conversación")
                .setMessage("¿Estás seguro de que quieres eliminar esta conversación permanentemente?")
                .setPositiveButton("Eliminar", (dialog, which) -> deleteConversation())
                .setNegativeButton("Cancelar", null)
                .setIcon(android.R.drawable.ic_dialog_alert)
                .show();
    }

    private void deleteConversation() {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        try {
            // The ON DELETE CASCADE in the messages table schema should handle deleting associated messages.
            int deletedRows = db.delete(ChatHistoryDbHelper.ConversationEntry.TABLE_NAME,
                    ChatHistoryDbHelper.ConversationEntry._ID + " = ?",
                    new String[]{String.valueOf(conversationId)});

            if (deletedRows > 0) {
                Log.d(TAG, "Conversation deleted successfully, ID: " + conversationId);
                Toast.makeText(this, "Conversación eliminada", Toast.LENGTH_SHORT).show();
                finish(); // Close this activity and return to the list
            } else {
                Log.w(TAG, "No conversation found to delete with ID: " + conversationId);
                Toast.makeText(this, "Error: No se encontró la conversación para eliminar", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error deleting conversation", e);
            Toast.makeText(this, "Error al eliminar conversación", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    protected void onDestroy() {
        dbHelper.close();
        super.onDestroy();
    }
} 