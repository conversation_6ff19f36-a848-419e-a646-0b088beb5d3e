<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="24dp"
    android:background="#f5f5f5">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="FloatingAI"
        android:textSize="32sp"
        android:textStyle="bold"
        android:textColor="#2196F3"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Burbuja flotante inteligente con IA\nSe inicia automáticamente al abrir la app"
        android:textSize="16sp"
        android:textAlignment="center"
        android:textColor="#666"
        android:layout_marginBottom="32dp" />

    <Button
        android:id="@+id/start_floating_button"
        android:layout_width="200dp"
        android:layout_height="60dp"
        android:text="Reiniciar Burbuja"
        android:textSize="18sp"
        android:textColor="#ffffff"
        android:background="#4CAF50"
        android:layout_marginBottom="16dp" />

    <Button
        android:id="@+id/settings_button"
        android:layout_width="200dp"
        android:layout_height="60dp"
        android:text="Configuración"
        android:textSize="18sp"
        android:textColor="#ffffff"
        android:background="#2196F3" />

    <Button
        android:id="@+id/chat_history_button"
        android:layout_width="200dp"
        android:layout_height="60dp"
        android:text="Historial de Chats"
        android:textSize="18sp"
        android:textColor="#ffffff"
        android:background="#FF9800"
        android:layout_marginTop="16dp" />

    <Button
        android:id="@+id/other_options_button"
        android:layout_width="200dp"
        android:layout_height="60dp"
        android:text="Otras Opciones"
        android:textSize="18sp"
        android:textColor="#ffffff"
        android:background="#607D8B"
        android:layout_marginTop="16dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="La burbuja flotante se inicia automáticamente\nUsa 'Reiniciar Burbuja' solo si es necesario"
        android:textSize="12sp"
        android:textAlignment="center"
        android:textColor="#999"
        android:layout_marginTop="24dp" />

</LinearLayout>