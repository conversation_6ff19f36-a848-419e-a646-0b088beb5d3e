package com.example.floatingai;

import android.content.Context;
import android.content.SharedPreferences;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class ModelManager {
    private static final String PREFS_NAME = "model_prefs";
    private static final String FAVORITE_MODELS_KEY = "favorite_models";
    
    private SharedPreferences prefs;
    private Gson gson;
    
    public ModelManager(Context context) {
        prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        gson = new Gson();
    }
    
    public static class AIModel {
        public String id;
        public String displayName;
        
        public AIModel(String id, String displayName) {
            this.id = id;
            this.displayName = displayName;
        }
        
        @Override
        public String toString() {
            return displayName;
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            AIModel aiModel = (AIModel) obj;
            return id.equals(aiModel.id);
        }
        
        @Override
        public int hashCode() {
            return id.hashCode();
        }
    }
    
    public List<AIModel> getFavoriteModels() {
        String json = prefs.getString(FAVORITE_MODELS_KEY, "[]");
        Type listType = new TypeToken<List<AIModel>>(){}.getType();
        List<AIModel> favorites = gson.fromJson(json, listType);
        return favorites != null ? favorites : new ArrayList<>();
    }
    
    public void addFavoriteModel(AIModel model) {
        List<AIModel> favorites = getFavoriteModels();
        
        // Verificar si ya existe
        for (AIModel existing : favorites) {
            if (existing.id.equals(model.id)) {
                return; // Ya existe, no agregar duplicado
            }
        }
        
        favorites.add(model);
        saveFavoriteModels(favorites);
    }
    
    public void removeFavoriteModel(String modelId) {
        List<AIModel> favorites = getFavoriteModels();
        favorites.removeIf(model -> model.id.equals(modelId));
        saveFavoriteModels(favorites);
    }
    
    public void clearFavoriteModels() {
        saveFavoriteModels(new ArrayList<>());
    }
    
    private void saveFavoriteModels(List<AIModel> models) {
        String json = gson.toJson(models);
        prefs.edit().putString(FAVORITE_MODELS_KEY, json).apply();
    }
    
    public List<AIModel> getAllModels() {
        List<AIModel> allModels = new ArrayList<>();
        allModels.addAll(getFavoriteModels());
        return allModels;
    }
    
    public AIModel findModelById(String modelId) {
        for (AIModel model : getFavoriteModels()) {
            if (model.id.equals(modelId)) {
                return model;
            }
        }
        return null;
    }
    
    public boolean isValidModelId(String modelId) {
        // Validación básica del formato del ID del modelo
        if (modelId == null || modelId.trim().isEmpty()) {
            return false;
        }
        
        // Debe contener al menos un "/"
        return modelId.contains("/") && modelId.length() > 3;
    }
    
    public String generateDisplayName(String modelId) {
        if (modelId == null || modelId.trim().isEmpty()) {
            return "Modelo Desconocido";
        }
        
        // Extraer nombre del proveedor y modelo
        String[] parts = modelId.split("/");
        if (parts.length >= 2) {
            String provider = parts[0];
            String modelName = parts[1];
            
            // Capitalizar primera letra
            provider = provider.substring(0, 1).toUpperCase() + provider.substring(1);
            modelName = modelName.substring(0, 1).toUpperCase() + modelName.substring(1);
            
            return provider + " - " + modelName;
        }
        
        return modelId;
    }
} 