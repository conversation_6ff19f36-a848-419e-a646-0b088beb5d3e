<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#ffffff"
    android:elevation="8dp">

    <!-- Header -->
    <LinearLayout
        android:id="@+id/chat_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:background="#DDDDDD"
        android:gravity="center_vertical">

        <ImageButton
            android:id="@+id/new_chat_button" 
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@android:drawable/ic_input_add" 
            android:background="@android:color/transparent"
            android:contentDescription="Nueva Conversación"
            android:tintMode="src_atop"
            android:tint="@android:color/black" />

        <TextView
            android:id="@+id/chat_window_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="FloatingAI Chat"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center_horizontal"
            android:textColor="@android:color/black" />

        <ImageButton
            android:id="@+id/select_model_button"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@android:drawable/ic_menu_preferences" 
            android:background="@android:color/transparent"
            android:contentDescription="Seleccionar Modelo"
            android:tintMode="src_atop"
            android:tint="@android:color/black" />

        <ImageButton
            android:id="@+id/close_button"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            android:background="@android:color/transparent"
            android:contentDescription="Cerrar Chat"
            android:tintMode="src_atop"
            android:tint="@android:color/holo_red_dark"/>
    </LinearLayout>

    <!-- Chat History -->
    <ScrollView
    android:id="@+id/chat_scroll"
    android:layout_width="match_parent"
    android:layout_height="0dp"
    android:layout_weight="1">

    <LinearLayout
        android:id="@+id/chat_history"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="8dp" />
</ScrollView>

    <!-- Input Area -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#ffffff"
        android:padding="12dp"
        android:elevation="2dp">

        <!-- Container for Prompt Buttons -->
        <LinearLayout
            android:id="@+id/prompt_buttons_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginBottom="8dp"
            android:visibility="gone" /> <!-- Initially hidden -->

        <!-- Message Input -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <EditText
                android:id="@+id/message_input"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:hint="Escribe tu pregunta..."
                android:textSize="14sp"
                android:padding="8dp"
                android:background="#f0f0f0"
                android:maxLines="3" />

            <Button
                android:id="@+id/send_button"
                android:layout_width="60dp"
                android:layout_height="40dp"
                android:text="→"
                android:textSize="16sp"
                android:textColor="#ffffff"
                android:background="#4CAF50"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp">

            <Button
                android:id="@+id/screenshot_button"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:text="📸 Captura"
                android:textSize="12sp"
                android:textColor="#ffffff"
                android:background="#FF9800"
                android:layout_marginEnd="4dp" />

            <Button
                android:id="@+id/gallery_button"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:text="🖼️ Galería"
                android:textSize="12sp"
                android:textColor="#ffffff"
                android:background="#9C27B0"
                android:layout_marginStart="4dp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout> 