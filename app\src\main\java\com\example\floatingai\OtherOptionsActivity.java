package com.example.floatingai;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.RadioGroup;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

public class OtherOptionsActivity extends AppCompatActivity {

    private EditText temperatureInput, contextWindowInput, maxTokensInput, systemPromptInput;
    private RadioGroup fontSizeGroup;
    private CheckBox sendWithEnterCheckbox;
    private Button saveOtherOptionsButton;
    private SharedPreferences sharedPreferences;

    // New fields for configurable buttons
    private EditText promptButton1TextInput, promptButton2TextInput;
    private CheckBox promptButton1EnableCheckbox, promptButton2EnableCheckbox;
    // Adding new fields for buttons 3 and 4
    private EditText promptButton3TextInput, promptButton4TextInput;
    private CheckBox promptButton3EnableCheckbox, promptButton4EnableCheckbox;

    public static final String PREFS_NAME = "app_prefs";
    public static final String KEY_TEMPERATURE = "ai_temperature";
    public static final String KEY_CONTEXT_WINDOW = "ai_context_window";
    public static final String KEY_MAX_TOKENS = "ai_max_tokens";
    public static final String KEY_FONT_SIZE = "chat_font_size";
    public static final String KEY_SEND_WITH_ENTER = "send_with_enter";
    public static final String KEY_SYSTEM_PROMPT = "ai_system_prompt";
    // New keys for configurable buttons
    public static final String KEY_PROMPT_BUTTON_1_TEXT = "prompt_button_1_text";
    public static final String KEY_PROMPT_BUTTON_1_ENABLED = "prompt_button_1_enabled";
    public static final String KEY_PROMPT_BUTTON_2_TEXT = "prompt_button_2_text";
    public static final String KEY_PROMPT_BUTTON_2_ENABLED = "prompt_button_2_enabled";
    // Adding new keys for buttons 3 and 4
    public static final String KEY_PROMPT_BUTTON_3_TEXT = "prompt_button_3_text";
    public static final String KEY_PROMPT_BUTTON_3_ENABLED = "prompt_button_3_enabled";
    public static final String KEY_PROMPT_BUTTON_4_TEXT = "prompt_button_4_text";
    public static final String KEY_PROMPT_BUTTON_4_ENABLED = "prompt_button_4_enabled";
    public static final String KEY_CLOSE_ON_OUTSIDE = "close_on_outside";
    public static final String KEY_SAVE_CAPTURES_IN_GALLERY = "save_captures_in_gallery";
    public static final boolean DEFAULT_CLOSE_ON_OUTSIDE = false;
    public static final boolean DEFAULT_SAVE_CAPTURES_IN_GALLERY = false;
    private CheckBox closeOnOutsideCheckbox;
    private CheckBox saveCapturesInGalleryCheckbox;

    // Default values
    public static final float DEFAULT_TEMPERATURE = 0.7f;
    public static final int DEFAULT_CONTEXT_WINDOW = 10; // Example: last 10 messages
    public static final int DEFAULT_MAX_TOKENS = 1000;
    public static final String DEFAULT_FONT_SIZE = "medium";
    public static final boolean DEFAULT_SEND_WITH_ENTER = false;
    public static final String DEFAULT_SYSTEM_PROMPT = "Eres un asistente de IA útil.";
    // New default values for configurable buttons
    public static final String DEFAULT_PROMPT_BUTTON_1_TEXT = "Explica esto";
    public static final boolean DEFAULT_PROMPT_BUTTON_1_ENABLED = true;
    public static final String DEFAULT_PROMPT_BUTTON_2_TEXT = "Resume esto";
    public static final boolean DEFAULT_PROMPT_BUTTON_2_ENABLED = true;
    // Adding default values for buttons 3 and 4
    public static final String DEFAULT_PROMPT_BUTTON_3_TEXT = "Analiza imagen";
    public static final boolean DEFAULT_PROMPT_BUTTON_3_ENABLED = true;
    public static final String DEFAULT_PROMPT_BUTTON_4_TEXT = "Traduce texto";
    public static final boolean DEFAULT_PROMPT_BUTTON_4_ENABLED = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_other_options);

        Toolbar toolbar = findViewById(R.id.toolbar_other_options);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("Otras Opciones");
        }

        sharedPreferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);

        temperatureInput = findViewById(R.id.temperature_input);
        contextWindowInput = findViewById(R.id.context_window_input);
        maxTokensInput = findViewById(R.id.max_tokens_input);
        fontSizeGroup = findViewById(R.id.font_size_group);
        sendWithEnterCheckbox = findViewById(R.id.send_with_enter_checkbox);
        saveOtherOptionsButton = findViewById(R.id.save_other_options_button);
        systemPromptInput = findViewById(R.id.system_prompt_input);

        // Initialize new UI elements
        promptButton1TextInput = findViewById(R.id.prompt_button_1_text_input);
        promptButton1EnableCheckbox = findViewById(R.id.prompt_button_1_enable_checkbox);
        promptButton2TextInput = findViewById(R.id.prompt_button_2_text_input);
        promptButton2EnableCheckbox = findViewById(R.id.prompt_button_2_enable_checkbox);
        promptButton3TextInput = findViewById(R.id.prompt_button_3_text_input);
        promptButton3EnableCheckbox = findViewById(R.id.prompt_button_3_enable_checkbox);
        promptButton4TextInput = findViewById(R.id.prompt_button_4_text_input);
        promptButton4EnableCheckbox = findViewById(R.id.prompt_button_4_enable_checkbox);
        closeOnOutsideCheckbox = findViewById(R.id.close_on_outside_checkbox);
        saveCapturesInGalleryCheckbox = findViewById(R.id.save_captures_in_gallery_checkbox);

        loadSettings();

        saveOtherOptionsButton.setOnClickListener(v -> saveSettings());
    }

    @Override
    protected void onPause() {
        super.onPause();
        saveSettings(); // Save settings when the activity is paused
    }

    private void loadSettings() {
        temperatureInput.setText(String.valueOf(sharedPreferences.getFloat(KEY_TEMPERATURE, DEFAULT_TEMPERATURE)));
        contextWindowInput.setText(String.valueOf(sharedPreferences.getInt(KEY_CONTEXT_WINDOW, DEFAULT_CONTEXT_WINDOW)));
        maxTokensInput.setText(String.valueOf(sharedPreferences.getInt(KEY_MAX_TOKENS, DEFAULT_MAX_TOKENS)));
        sendWithEnterCheckbox.setChecked(sharedPreferences.getBoolean(KEY_SEND_WITH_ENTER, DEFAULT_SEND_WITH_ENTER));
        systemPromptInput.setText(sharedPreferences.getString(KEY_SYSTEM_PROMPT, DEFAULT_SYSTEM_PROMPT));

        // Load settings for configurable buttons
        promptButton1TextInput.setText(sharedPreferences.getString(KEY_PROMPT_BUTTON_1_TEXT, DEFAULT_PROMPT_BUTTON_1_TEXT));
        promptButton1EnableCheckbox.setChecked(sharedPreferences.getBoolean(KEY_PROMPT_BUTTON_1_ENABLED, DEFAULT_PROMPT_BUTTON_1_ENABLED));
        promptButton2TextInput.setText(sharedPreferences.getString(KEY_PROMPT_BUTTON_2_TEXT, DEFAULT_PROMPT_BUTTON_2_TEXT));
        promptButton2EnableCheckbox.setChecked(sharedPreferences.getBoolean(KEY_PROMPT_BUTTON_2_ENABLED, DEFAULT_PROMPT_BUTTON_2_ENABLED));
        promptButton3TextInput.setText(sharedPreferences.getString(KEY_PROMPT_BUTTON_3_TEXT, DEFAULT_PROMPT_BUTTON_3_TEXT));
        promptButton3EnableCheckbox.setChecked(sharedPreferences.getBoolean(KEY_PROMPT_BUTTON_3_ENABLED, DEFAULT_PROMPT_BUTTON_3_ENABLED));
        promptButton4TextInput.setText(sharedPreferences.getString(KEY_PROMPT_BUTTON_4_TEXT, DEFAULT_PROMPT_BUTTON_4_TEXT));
        promptButton4EnableCheckbox.setChecked(sharedPreferences.getBoolean(KEY_PROMPT_BUTTON_4_ENABLED, DEFAULT_PROMPT_BUTTON_4_ENABLED));
        closeOnOutsideCheckbox.setChecked(sharedPreferences.getBoolean(KEY_CLOSE_ON_OUTSIDE, DEFAULT_CLOSE_ON_OUTSIDE));
        saveCapturesInGalleryCheckbox.setChecked(sharedPreferences.getBoolean(KEY_SAVE_CAPTURES_IN_GALLERY, DEFAULT_SAVE_CAPTURES_IN_GALLERY));

        String fontSize = sharedPreferences.getString(KEY_FONT_SIZE, DEFAULT_FONT_SIZE);
        if ("small".equals(fontSize)) {
            fontSizeGroup.check(R.id.font_small);
        } else if ("large".equals(fontSize)) {
            fontSizeGroup.check(R.id.font_large);
        } else {
            fontSizeGroup.check(R.id.font_medium);
        }
    }

    private void saveSettings() {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        try {
            editor.putFloat(KEY_TEMPERATURE, Float.parseFloat(temperatureInput.getText().toString()));
            editor.putInt(KEY_CONTEXT_WINDOW, Integer.parseInt(contextWindowInput.getText().toString()));
            editor.putInt(KEY_MAX_TOKENS, Integer.parseInt(maxTokensInput.getText().toString()));
            editor.putBoolean(KEY_SEND_WITH_ENTER, sendWithEnterCheckbox.isChecked());
            editor.putString(KEY_SYSTEM_PROMPT, systemPromptInput.getText().toString());

            // Save settings for configurable buttons
            editor.putString(KEY_PROMPT_BUTTON_1_TEXT, promptButton1TextInput.getText().toString());
            editor.putBoolean(KEY_PROMPT_BUTTON_1_ENABLED, promptButton1EnableCheckbox.isChecked());
            editor.putString(KEY_PROMPT_BUTTON_2_TEXT, promptButton2TextInput.getText().toString());
            editor.putBoolean(KEY_PROMPT_BUTTON_2_ENABLED, promptButton2EnableCheckbox.isChecked());
            editor.putString(KEY_PROMPT_BUTTON_3_TEXT, promptButton3TextInput.getText().toString());
            editor.putBoolean(KEY_PROMPT_BUTTON_3_ENABLED, promptButton3EnableCheckbox.isChecked());
            editor.putString(KEY_PROMPT_BUTTON_4_TEXT, promptButton4TextInput.getText().toString());
            editor.putBoolean(KEY_PROMPT_BUTTON_4_ENABLED, promptButton4EnableCheckbox.isChecked());
            editor.putBoolean(KEY_CLOSE_ON_OUTSIDE, closeOnOutsideCheckbox.isChecked());
            editor.putBoolean(KEY_SAVE_CAPTURES_IN_GALLERY, saveCapturesInGalleryCheckbox.isChecked());

            int selectedFontId = fontSizeGroup.getCheckedRadioButtonId();
            String fontSize = DEFAULT_FONT_SIZE;
            if (selectedFontId == R.id.font_small) {
                fontSize = "small";
            } else if (selectedFontId == R.id.font_large) {
                fontSize = "large";
            }
            editor.putString(KEY_FONT_SIZE, fontSize);

            boolean success = editor.commit();
            if (success) {
                Toast.makeText(this, "Configuración guardada", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, "Error al guardar configuración", Toast.LENGTH_LONG).show();
            }
        } catch (NumberFormatException e) {
            Toast.makeText(this, "Error: Verifica los valores numéricos", Toast.LENGTH_LONG).show();
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish(); // Go back to the previous activity
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
} 