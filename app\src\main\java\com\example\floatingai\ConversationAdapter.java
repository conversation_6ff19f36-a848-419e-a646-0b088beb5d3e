package com.example.floatingai;

import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class ConversationAdapter extends RecyclerView.Adapter<ConversationAdapter.ConversationViewHolder> {

    private Context context;
    private List<ConversationItem> conversationList;

    public interface OnConversationClickListener {
        void onConversationClick(long conversationId);
    }

    private OnConversationClickListener clickListener;

    public ConversationAdapter(Context context, List<ConversationItem> initialConversationList, OnConversationClickListener listener) {
        this.context = context;
        this.conversationList = new ArrayList<>(initialConversationList);
        this.clickListener = listener;
    }

    @NonNull
    @Override
    public ConversationViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_conversation, parent, false);
        return new ConversationViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ConversationViewHolder holder, int position) {
        ConversationItem item = conversationList.get(position);
        holder.bind(item, clickListener);
    }

    @Override
    public int getItemCount() {
        return conversationList.size();
    }

    public void updateData(List<ConversationItem> newConversationList) {
        this.conversationList.clear();
        if (newConversationList != null) {
            this.conversationList.addAll(newConversationList);
        }
        notifyDataSetChanged();
    }

    static class ConversationViewHolder extends RecyclerView.ViewHolder {
        TextView timestampText;
        TextView idText;

        ConversationViewHolder(View itemView) {
            super(itemView);
            timestampText = itemView.findViewById(R.id.conversation_timestamp_text);
            idText = itemView.findViewById(R.id.conversation_id_text);
        }

        void bind(final ConversationItem item, final OnConversationClickListener listener) {
            SimpleDateFormat sdf = new SimpleDateFormat("MMM dd, yyyy HH:mm:ss", Locale.getDefault());
            String formattedDate = sdf.format(new Date(item.getTimestamp()));
            timestampText.setText(formattedDate);
            idText.setText("ID: " + item.getId());

            itemView.setOnClickListener(v -> listener.onConversationClick(item.getId()));
        }
    }

    // Helper class for conversation items
    public static class ConversationItem {
        private long id;
        private long timestamp;

        public ConversationItem(long id, long timestamp) {
            this.id = id;
            this.timestamp = timestamp;
        }

        public long getId() {
            return id;
        }

        public long getTimestamp() {
            return timestamp;
        }
    }
} 