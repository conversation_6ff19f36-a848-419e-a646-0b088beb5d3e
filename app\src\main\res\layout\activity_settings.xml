<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="#f5f5f5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Configuración de FloatingAI"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="#2196F3"
            android:layout_marginBottom="24dp" />

        <!-- API Key Section -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="API Key de OpenRouter"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#333"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Obtén tu API key gratuita en openrouter.ai"
            android:textSize="12sp"
            android:textColor="#666"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/api_key_input"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:hint="sk-or-v1-..."
            android:inputType="textPassword"
            android:background="#ffffff"
            android:padding="12dp"
            android:layout_marginBottom="24dp" />

        <!-- Model Selection Section -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Selección de Modelo"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="#2196F3"
            android:layout_marginBottom="16dp" />

        <!-- Predefined Models -->
        <!-- TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Modelos Predefinidos"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#333"
            android:layout_marginBottom="8dp" / -->

        <!-- Spinner
            android:id="@+id/model_spinner"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:background="#ffffff"
            android:layout_marginBottom="16dp" / -->

        <!-- Custom Model Section -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Modelo Personalizado"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#333"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Ingresa el ID exacto del modelo (ej: anthropic/claude-3-opus)"
            android:textSize="12sp"
            android:textColor="#666"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <EditText
                android:id="@+id/custom_model_input"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:hint="ID del modelo"
                android:inputType="text"
                android:background="#ffffff"
                android:padding="12dp"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/add_custom_model_button"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:text="Agregar"
                android:textColor="#ffffff"
                android:background="#FF9800"
                android:minWidth="0dp"
                android:paddingHorizontal="16dp" />

        </LinearLayout>

        <EditText
            android:id="@+id/custom_model_name_input"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:hint="Nombre amigable (opcional)"
            android:inputType="text"
            android:background="#ffffff"
            android:padding="12dp"
            android:layout_marginBottom="16dp" />

        <!-- Favorite Models Section -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Modelos Favoritos"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#333"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Toca para seleccionar, mantén presionado para eliminar"
            android:textSize="12sp"
            android:textColor="#666"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:id="@+id/favorite_models_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="24dp" />

        <!-- Current Selection Display -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Modelo Seleccionado"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#333"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/selected_model_display"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Ninguno seleccionado"
            android:textSize="14sp"
            android:textColor="#666"
            android:background="#e8f5e8"
            android:padding="12dp"
            android:layout_marginBottom="24dp" />

        <!-- Save Button -->
        <Button
            android:id="@+id/save_settings_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Guardar Configuración"
            android:layout_marginTop="32dp" />

        <!-- Clear Favorites Button -->
        <Button
            android:id="@+id/clear_favorites_button"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="Limpiar Favoritos"
            android:textSize="14sp"
            android:textColor="#ffffff"
            android:background="#F44336"
            android:layout_marginBottom="16dp" />

        <!-- Info Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="💡 Consejos:\n• Los modelos favoritos se guardan localmente\n• Puedes usar cualquier modelo disponible en OpenRouter\n• Verifica que el ID del modelo sea exacto para evitar errores"
            android:textSize="12sp"
            android:textColor="#666"
            android:background="#e8f5e8"
            android:padding="12dp"
            android:layout_marginTop="8dp" />

    </LinearLayout>

</ScrollView>