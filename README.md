# FloatingAI - Burbuja Flotante Inteligente

Una aplicación Android que proporciona una burbuja flotante inteligente con capacidades de IA para análisis de pantalla y conversaciones.

## 🚀 Características

- **Burbuja Flotante**: Botón flotante que permanece visible sobre otras aplicaciones
- **Chat con IA**: Interfaz de conversación flotante integrada con OpenRouter API
- **Captura de Pantalla**: Captura y análisis inteligente de pantallas con IA
- **Múltiples Modelos**: Soporte para varios modelos de IA (Llama, GPT-4, Claude, Gemini)
- **Arrastrable**: La burbuja se puede mover libremente por la pantalla
- **Foreground Service**: Mantiene la funcionalidad activa en segundo plano

## 📱 Requisitos

- Android 11 (API 30) o superior
- Permisos de overlay (ventanas flotantes)
- Conexión a internet
- API Key de OpenRouter (gratuita disponible)

## 🛠️ Instalación

1. **Clonar el repositorio**:
   ```bash
   git clone https://github.com/tu-usuario/FloatingAI.git
   cd FloatingAI
   ```

2. **Abrir en Android Studio**:
   - Abre Android Studio
   - Selecciona "Open an existing project"
   - Navega a la carpeta del proyecto

3. **Configurar dependencias**:
   - El proyecto se sincronizará automáticamente
   - Asegúrate de tener Android SDK 35 instalado

4. **Compilar e instalar**:
   ```bash
   ./gradlew assembleDebug
   adb install app/build/outputs/apk/debug/app-debug.apk
   ```

## ⚙️ Configuración

1. **Obtener API Key de OpenRouter**:
   - Visita [openrouter.ai](https://openrouter.ai)
   - Crea una cuenta gratuita
   - Genera tu API key

2. **Configurar la aplicación**:
   - Abre FloatingAI
   - Toca "Configuración"
   - Ingresa tu API key de OpenRouter
   - Selecciona el modelo de IA deseado
   - Guarda la configuración

3. **Conceder permisos**:
   - La app solicitará permisos de overlay automáticamente
   - Acepta los permisos para captura de pantalla cuando sea necesario

## 🎯 Uso

### Iniciar la Burbuja Flotante
1. Abre la aplicación
2. Toca "Iniciar Burbuja"
3. La aplicación se minimizará y aparecerá la burbuja flotante

### Chat con IA
1. Toca la burbuja flotante (🤖)
2. Se abrirá la ventana de chat
3. Escribe tu pregunta y toca "→"
4. La IA responderá en tiempo real

### Captura y Análisis de Pantalla
1. En la ventana de chat, toca "📸 Captura"
2. Concede permisos de captura de pantalla si es necesario
3. La pantalla se capturará automáticamente
4. La IA analizará la imagen y proporcionará información

### Mover la Burbuja
- Mantén presionada la burbuja y arrástrala a cualquier posición
- La burbuja permanecerá en la nueva posición

## 🏗️ Arquitectura del Proyecto

```
app/
├── src/main/
│   ├── java/com/example/floatingai/
│   │   ├── MainActivity.java          # Actividad principal
│   │   ├── FloatingService.java       # Servicio de burbuja flotante
│   │   ├── ScreenCaptureActivity.java # Captura de pantalla
│   │   ├── SettingsActivity.java      # Configuración
│   │   └── OpenRouterAPI.java         # Integración con API
│   ├── res/
│   │   ├── layout/                    # Layouts XML
│   │   ├── drawable/                  # Recursos gráficos
│   │   └── values/                    # Strings, colores, etc.
│   └── AndroidManifest.xml           # Configuración de la app
├── build.gradle                      # Dependencias del módulo
└── proguard-rules.pro                # Reglas de ofuscación
```

## 🔧 Dependencias Principales

- **Retrofit 2.9.0**: Cliente HTTP para API calls
- **OkHttp 4.12.0**: Interceptor de logging
- **Gson 2.10.1**: Serialización JSON
- **Glide 4.16.0**: Manejo de imágenes
- **AndroidX**: Componentes de soporte

## 🎨 Modelos de IA Soportados

- **Llama 3.2 11B Vision** (Gratuito) - Recomendado para empezar
- **GPT-4o Mini** - Rápido y eficiente
- **Claude 3.5 Sonnet** - Excelente para análisis detallado
- **Gemini Pro Vision** - Potente para visión por computadora

## 🔒 Permisos Requeridos

```xml
<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

## 🐛 Solución de Problemas

### La burbuja no aparece
- Verifica que los permisos de overlay estén concedidos
- Ve a Configuración > Aplicaciones > FloatingAI > Permisos

### Error de API
- Verifica que tu API key sea correcta
- Asegúrate de tener conexión a internet
- Comprueba que el modelo seleccionado esté disponible

### Captura de pantalla no funciona
- Concede permisos de captura de pantalla cuando se soliciten
- Reinicia la aplicación si es necesario

## 🤝 Contribuir

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver `LICENSE` para más detalles.

## 🙏 Agradecimientos

- [OpenRouter](https://openrouter.ai) por proporcionar acceso a múltiples modelos de IA
- [Retrofit](https://square.github.io/retrofit/) por la excelente biblioteca HTTP
- La comunidad de Android por las guías y ejemplos

## 📞 Soporte

Si tienes problemas o preguntas:
- Abre un issue en GitHub
- Revisa la documentación de OpenRouter
- Consulta los logs de Android Studio para debugging

---

**¡Disfruta usando FloatingAI! 🤖✨** 