package com.example.floatingai;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import android.os.Bundle;
import android.content.Intent;
import android.view.View;  // For button click
import android.widget.Button;  // For button
import android.widget.Toast;
import android.net.Uri;
import android.provider.Settings;
import android.content.pm.PackageManager;
import android.Manifest;
import android.os.Build;
import android.content.Context;
import java.io.InputStream;
import android.util.Log;

public class MainActivity extends AppCompatActivity {

    private static final int PERMISSION_REQUEST_CODE = 1001;
    private static final int OVERLAY_PERMISSION_REQUEST_CODE = 1002;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // Verificar si la actividad fue iniciada por compartir una imagen
        handleSharedImage();

        // Verificar y solicitar permisos, luego iniciar automáticamente
        checkAndRequestPermissions();

        // Configurar botón de configuración
        Button settingsButton = findViewById(R.id.settings_button);
        if (settingsButton != null) {
            settingsButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent settingsIntent = new Intent(MainActivity.this, SettingsActivity.class);
                    startActivity(settingsIntent);
                }
            });
        }

        // Configurar botón para iniciar servicio flotante (ahora como respaldo manual)
        Button startFloatingButton = findViewById(R.id.start_floating_button);
        if (startFloatingButton != null) {
            // Cambiar el texto del botón para reflejar que es un respaldo
            startFloatingButton.setText("Reiniciar Burbuja");
            startFloatingButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    startFloatingService();
                }
            });
        }

        // Configurar botón de historial de chats
        Button chatHistoryButton = findViewById(R.id.chat_history_button);
        if (chatHistoryButton != null) {
            chatHistoryButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent chatHistoryIntent = new Intent(MainActivity.this, ChatHistoryActivity.class);
                    startActivity(chatHistoryIntent);
                }
            });
        }

        // Configurar botón de otras opciones
        Button otherOptionsButton = findViewById(R.id.other_options_button);
        if (otherOptionsButton != null) {
            otherOptionsButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent otherOptionsIntent = new Intent(MainActivity.this, OtherOptionsActivity.class);
                    startActivity(otherOptionsIntent);
                }
            });
        }
    }

    private void handleSharedImage() {
        Intent intent = getIntent();
        String action = intent.getAction();
        String type = intent.getType();

        if (Intent.ACTION_SEND.equals(action) && type != null && type.startsWith("image/")) {
            Uri imageUri = intent.getParcelableExtra(Intent.EXTRA_STREAM);
            if (imageUri != null) {
                // Iniciar el servicio flotante si no está corriendo
                startFloatingService();
                
                // Enviar la imagen al servicio flotante
                Intent serviceIntent = new Intent(this, FloatingService.class);
                serviceIntent.setAction("PROCESS_SHARED_IMAGE");
                serviceIntent.putExtra("image_uri", imageUri.toString());
                startService(serviceIntent);
                
                Toast.makeText(this, "Imagen recibida. Abre la burbuja flotante para analizarla.", Toast.LENGTH_LONG).show();
                
                // Minimizar la app
                moveTaskToBack(true);
            }
        }
    }

    private void checkAndRequestPermissions() {
        Log.d("MainActivity", "checkAndRequestPermissions() called");
        // Verificar permiso de overlay
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(this)) {
                Log.d("MainActivity", "Overlay permission NOT granted. Requesting...");
                Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                        Uri.parse("package:" + getPackageName()));
                startActivityForResult(intent, OVERLAY_PERMISSION_REQUEST_CODE);
                return;
            }
            Log.d("MainActivity", "Overlay permission already granted.");
        }

        // Verificar otros permisos
        String[] permissions = {
                Manifest.permission.WRITE_EXTERNAL_STORAGE,
                Manifest.permission.READ_EXTERNAL_STORAGE
        };

        boolean allPermissionsGranted = true;
        for (String permission : permissions) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                allPermissionsGranted = false;
                break;
            }
        }

        if (!allPermissionsGranted) {
            Log.d("MainActivity", "Storage permissions NOT granted. Requesting...");
            ActivityCompat.requestPermissions(this, permissions, PERMISSION_REQUEST_CODE);
        } else {
            Log.d("MainActivity", "All permissions already granted. Calling autoStartFloatingService.");
            autoStartFloatingService();
        }
    }

    private void startFloatingService() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
            Toast.makeText(this, "Se necesita permiso de overlay para mostrar la burbuja flotante", Toast.LENGTH_LONG).show();
            return;
        }

        Intent serviceIntent = new Intent(this, FloatingService.class);
        startService(serviceIntent);
        Toast.makeText(this, "Burbuja flotante reiniciada", Toast.LENGTH_SHORT).show();
        
        // Minimizar la app
        moveTaskToBack(true);
    }

    private void autoStartFloatingService() {
        Log.d("MainActivity", "autoStartFloatingService() called");
        // Verificar permiso de overlay primero
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
            Log.d("MainActivity", "autoStartFloatingService: Overlay permission still not granted. Returning.");
            return;
        }

        boolean serviceRunning = isServiceRunning(FloatingService.class);
        Log.d("MainActivity", "autoStartFloatingService: isServiceRunning = " + serviceRunning);

        // Verificar si el servicio ya está corriendo
        if (serviceRunning) {
            Toast.makeText(this, "La burbuja flotante ya está activa.", Toast.LENGTH_SHORT).show();
            Log.d("MainActivity", "autoStartFloatingService: Service already running. MainActivity remains visible.");
        } else {
            Log.d("MainActivity", "autoStartFloatingService: Service not running. Starting service and minimizing app.");
            Intent serviceIntent = new Intent(this, FloatingService.class);
            startService(serviceIntent);
            Toast.makeText(this, "Burbuja flotante iniciada automáticamente.", Toast.LENGTH_SHORT).show();
            moveTaskToBack(true); // Minimizar la app solo cuando se inicia el servicio por primera vez automáticamente.
        }
    }

    private boolean isServiceRunning(Class<?> serviceClass) {
        android.app.ActivityManager manager = (android.app.ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        for (android.app.ActivityManager.RunningServiceInfo service : manager.getRunningServices(Integer.MAX_VALUE)) {
            if (serviceClass.getName().equals(service.service.getClassName())) {
                return true;
            }
        }
        return false;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (requestCode == OVERLAY_PERMISSION_REQUEST_CODE) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && Settings.canDrawOverlays(this)) {
                Toast.makeText(this, "Permiso de overlay concedido", Toast.LENGTH_SHORT).show();
                // Ahora que el permiso de overlay está, verificar los otros permisos e iniciar.
                checkAndRequestPermissions(); 
            } else {
                Toast.makeText(this, "Permiso de overlay denegado. La burbuja no funcionará.", Toast.LENGTH_LONG).show();
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        Log.d("MainActivity", "onRequestPermissionsResult() called with requestCode: " + requestCode);
        
        if (requestCode == PERMISSION_REQUEST_CODE) {
            boolean allGranted = true;
            if (grantResults.length > 0) {
                for (int result : grantResults) {
                    if (result != PackageManager.PERMISSION_GRANTED) {
                        allGranted = false;
                        // We don't break here anymore, to check all results for logging if needed
                    }
                }
            } else { // No grant results usually means the request was cancelled or interrupted
                allGranted = false;
            }

            Log.d("MainActivity", "onRequestPermissionsResult: allGranted = " + allGranted);

            if (allGranted) {
                Log.d("MainActivity", "onRequestPermissionsResult: All permissions granted. Calling autoStartFloatingService.");
                autoStartFloatingService();
            } else {
                Log.d("MainActivity", "onRequestPermissionsResult: Some permissions denied. Attempting to start floating service anyway.");
                // Attempt to start the service even if storage permissions are denied.
                // The FloatingService should handle the lack of these permissions gracefully.
                autoStartFloatingService(); 
            }
        }
    }
}