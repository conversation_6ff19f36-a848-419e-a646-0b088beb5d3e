<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.FloatingAI" parent="Theme.AppCompat.DayNight">
        <!-- Customize your theme here. -->
    </style>

    <!-- Theme for activities that will use their own Toolbar -->
    <style name="Theme.FloatingAI.NoActionBar" parent="Theme.AppCompat.DayNight.NoActionBar">
        <!-- Customize your theme here if needed -->
    </style>

    <style name="DialogStyle" parent="Theme.AppCompat.Light.Dialog.Alert">
        <!-- Add any specific dialog customizations here if needed -->
        <item name="android:background">#FFFFFF</item> <!-- Example: Set a white background -->
        <item name="android:textColorPrimary">@android:color/black</item>
        <item name="colorAccent">@color/colorPrimary</item> <!-- Or your app's accent color -->
    </style>
</resources> 