<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Per<PERSON><PERSON> necesarios -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.INTERNET" />

    <!-- Permisos de almacenamiento con compatibilidad para diferentes versiones -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- Declarar compatibilidad con diferentes arquitecturas -->
    <supports-screens
        android:largeScreens="true"
        android:normalScreens="true"
        android:smallScreens="true"
        android:xlargeScreens="true" />

    <application
        android:allowBackup="true"
        android:icon="@android:drawable/ic_dialog_info"
        android:label="FloatingAI"
        android:roundIcon="@android:drawable/ic_dialog_info"
        android:supportsRtl="true"
        android:usesCleartextTraffic="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:theme="@style/Theme.FloatingAI"
        android:requestLegacyExternalStorage="true"
        android:preserveLegacyExternalStorage="true"
        tools:targetApi="31">
        
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="image/*" />
            </intent-filter>
        </activity>

        <activity 
            android:name=".SettingsActivity" 
            android:exported="false"
            android:parentActivityName=".MainActivity"
            android:label="Configuración IA">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
            </intent-filter>
        </activity>

        <!-- Activity para selección de área -->
        <activity
            android:name=".AreaSelectionActivity"
            android:exported="false"
            android:theme="@style/Theme.AreaSelection" />

        <!-- Activity para configurar accesibilidad -->
        <activity
            android:name=".AccessibilitySetupActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Material.Light.Dialog" />

        <!-- Activity para ver el historial de chat -->
        <activity 
            android:name=".ChatHistoryActivity"
            android:label="Historial de Chats"
            android:parentActivityName=".MainActivity"> 
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".SettingsActivity" />
        </activity>

        <!-- Activity para ver los mensajes de una conversación específica -->
        <activity
            android:name=".ConversationViewActivity"
            android:label="Ver Conversación"
            android:parentActivityName=".ChatHistoryActivity">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".ChatHistoryActivity" />
        </activity>

        <!-- Activity para otras opciones -->
        <activity
            android:name=".OtherOptionsActivity"
            android:label="Otras Opciones"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.FloatingAI.NoActionBar" />

        <!-- Service para la burbuja flotante -->
        <service 
            android:name=".FloatingService"
            android:exported="false"
            android:foregroundServiceType="dataSync">
        </service>
        
        <!-- Servicio de Accesibilidad para captura de pantalla -->
        <service
            android:name=".ScreenCaptureAccessibilityService"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
            android:exported="true">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility_service_config" />
        </service>
    </application>
</manifest>